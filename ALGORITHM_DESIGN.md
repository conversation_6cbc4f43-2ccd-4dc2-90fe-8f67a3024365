# 核心算法设计

**文档状态**: 草案
**所有者**: 技术负责人

---

## 1. 目的

本文档旨在详细说明 Upbase 应用中各项核心算法和业务逻辑规则的设计，为开发团队提供清晰、可执行的实现指南。这些算法是实现产品个性化、游戏化和智能化体验的基石。

---

## 2. 个性化戒断计划生成算法 (v1.0)

此算法在用户完成初始设置后触发，旨在为用户创建一个为期30天的、结构化的、可执行的初始计划。

### 2.1. 输入参数

-   `age`: 用户年龄 (number)
-   `goal`: 用户设定的主要目标 (string: '戒除色情', '减少频率', '自我提升')
-   `history`: 用户的戒断历史 (string: '新手', '尝试过但失败', '有一定经验')
-   `triggers`: 用户选择的主要触发因素 (array of strings: '深夜独处', '压力大', '无聊', '社交媒体')

### 2.2. 算法逻辑

算法通过一个基于规则的评分系统来从“任务库”中挑选并组合每日任务。

1.  **任务库 (Task Pool)**: 一个预定义的任务列表，每个任务包含以下属性：
    -   `id`: 任务ID
    -   `title`: 任务标题 (e.g., "进行10分钟正念冥想")
    -   `category`: 任务类别 ('mindfulness', 'physical', 'learning', 'hobby')
    -   `difficulty`: 难度 (1-5)
    -   `baseScore`: 基础分数 (10)
    -   `tags`: 关联标签 (e.g., '应对压力', '提升专注力')

2.  **计分与选择流程**: 
    -   **遍历任务库**: 对库中每一个任务，根据用户输入参数计算其“匹配分”。
    -   **分数计算公式**: `匹配分 = 基础分 + 年龄权重 + 目标权重 + 历史权重 + 触发因素权重`
        -   **年龄权重**: 年轻用户（<22岁）增加“体育锻炼”类任务权重；年长用户增加“正念”、“学习”类任务权重。
        -   **目标权重**: 若`goal`为“自我提升”，则“学习”、“爱好”类任务权重增加。
        -   **历史权重**: 若`history`为“新手”，则`difficulty`低的任务权重增加；若为“有一定经验”，则`difficulty`高的任务权重增加。
        -   **触发因素权重**: 若任务`tags`与用户的`triggers`匹配（如任务标签为“应对压力”，用户触发因素含“压力大”），则该任务权重显著增加。
    -   **生成每日计划**: 
        -   算法为未来30天的每一天，从任务库中选择2-3个总分最高的、且类别不同的任务。
        -   计划的整体难度会随天数递增（例如，前7天主要选择`difficulty` < 3的任务）。

### 2.3. 输出

-   一个包含30天每日任务列表的JSON对象，存储于`RecoveryPlan`表中。

---

## 3. “正气值”计算模型 (v1.0)

“正气值”是一个游戏化的动态评分，旨在直观地反映用户近期的努力程度和健康状态。

### 3.1. 核心原则

-   **基于行为**: 分数完全由用户的具体行为决定。
-   **即时反馈**: 用户的每一个关键行为都会立即影响分数。
-   **衰减机制**: 分数会随时间自然衰减，鼓励用户持续努力。

### 3.2. 计算公式

`每日正气值 = (昨日正气值 * 衰减因子) + 今日行为得分`

-   **衰减因子 (Decay Factor)**: `0.98`。表示如果一天没有任何行为，正气值会下降2%。
-   **初始值**: 新用户初始值为 `100`。
-   **分数范围**: 0 - 1000 (暂定)

### 3.3. 行为得分表

| 行为                     | 分数变化 | 备注                                     |
| ------------------------ | -------- | ---------------------------------------- |
| **成功打卡**             | `+10`    | 每日一次                                 |
| **完成一个每日任务**     | `+5`     | 每个任务                                 |
| **在社区发布有意义的帖子** | `+8`     | AI判断内容质量，低质量或灌水不得分       |
| **在社区获得一个赞**     | `+1`     |                                          |
| **完成一篇教育文章学习** | `+5`     |                                          |
| **使用一次“急救包”**     | `+3`     | 鼓励用户在冲动时寻求帮助                 |
| **记录一次破戒**         | `-50`    | 惩罚力度较大，但不是毁灭性的             |
| **连续7天成功打卡**      | `+30`    | 额外奖励                                 |
| **连续30天成功打卡**     | `+100`   | 额外奖励                                 |

---

## 4. 内容推荐引擎逻辑 (v1.0)

初版推荐引擎采用基于内容的过滤 (Content-Based Filtering) 算法，简单、高效且无需大量用户数据。

### 4.1. 内容与用户标签化

-   **内容侧**: 每篇教育文章、每个冥想资源都有一组由编辑设定的标签。例如：`{id: 'article-101', tags: ['科学原理', '多巴胺', '新手必看']}`。
-   **用户侧**: 系统根据用户的行为动态生成一个用户兴趣画像（标签向量）。
    -   `用户标签 = (初始设置中的触发因素) + (近期阅读文章的标签) + (近期完成任务的标签) + (社区发帖内容的关键词)`

### 4.2. 推荐逻辑

1.  **获取用户标签向量**: 从用户画像中获取当前最显著的几个兴趣标签，例如 `['应对压力', '提升专注力', '新手']`。
2.  **候选集召回**: 从所有内容库中，找出所有包含这些标签的内容，作为候选推荐集。
3.  **内容排序 (Scoring)**:
    -   **计算匹配度**: 对候选集中的每一项内容，计算其标签与用户标签向量的重合度（例如，使用Jaccard相似度）。
    -   **加入权重**: 
        -   **新颖度**: 最近发布的内容有更高的权重。
        -   **热度**: 近期被很多用户阅读或点赞的内容有更高的权重。
        -   **个性化**: 用户从未看过的内容权重高于已看过的内容。
    -   **最终排序**: `最终得分 = 匹配度分 * 0.6 + 新颖度分 * 0.2 + 热度分 * 0.2`
4.  **输出**: 在首页或“推荐阅读”模块，向用户展示得分最高的3-5项内容。
