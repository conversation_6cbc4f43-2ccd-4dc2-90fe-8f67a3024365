# 国际化 (i18n) 策略

**文档状态**: 草案
**所有者**: 前端负责人

---

## 1. 目的

本文档旨在为 Upbase 应用制定一套清晰、可扩展的国际化（i18n）和本地化（l10n）策略。其目标是确保应用能够高效地支持多种语言，为全球用户提供母语化的用户体验，并为未来的市场扩展奠定技术基础。

---

## 2. 策略概述

-   **默认语言**: **简体中文 (zh-CN)** 是项目的源语言和默认语言。
-   **目标语言 (第一阶段)**: **英语 (en)** 将作为我们支持的第一个额外语言。
-   **技术栈**: 我们将使用 **`next-i18next`** 库，它与 Next.js 生态系统深度集成，支持服务端渲染（SSR）和静态站点生成（SSG）的 i18n。
-   **路径路由**: 我们将采用子路径路由策略来区分语言版本。例如：
    -   `https://upbase.app/` (默认语言，简体中文)
    -   `https://upbase.app/en/` (英语版本)

---

## 3. 翻译资源管理

### 3.1. 文件结构

-   所有的翻译文本将以 JSON 格式存储在 `public/locales/{lang}/{namespace}.json` 目录下。
-   **`{lang}`**: 语言代码 (e.g., `zh-CN`, `en`).
-   **`{namespace}`**: 命名空间，用于组织和分割翻译内容，避免单个文件过于庞大，并实现按需加载。

```
public/
└── locales/
    ├── zh-CN/
    │   ├── common.json       # 通用文本 (按钮、标签等)
    │   ├── auth.json         # 认证流程相关文本
    │   └── community.json    # 社区模块相关文本
    └── en/
        ├── common.json
        ├── auth.json
        └── community.json
```

### 3.2. 键 (Key) 命名规范

-   使用 **结构化、点分隔** 的键名，以反映其在UI中的位置和功能。
-   键名应清晰、易于理解。

**示例 (`common.json`):**
```json
{
  "button": {
    "save": "保存",
    "cancel": "取消",
    "submit": "提交"
  },
  "label": {
    "email": "电子邮箱",
    "password": "密码"
  }
}
```

### 3.3. 内容处理

-   **复数**: 使用 `next-i18next` 的复数功能处理不同语言的复数规则。
    ```json
    "day_one": "{{count}} 天",
    "day_other": "{{count}} 天"
    ```
-   **插值**: 在翻译字符串中使用 `{{variable}}` 格式进行动态内容的插值。
-   **富文本/HTML**: 对于包含链接或格式的文本，使用 `next-i18next` 的 `Trans` 组件来安全地渲染HTML。

---

## 4. 技术实现方案

### 4.1. 配置

1.  **安装依赖**: `npm install next-i18next react-i18next i18next`
2.  **创建配置文件**: 在项目根目录创建 `next-i18next.config.js`。
    ```javascript
    module.exports = {
      i18n: {
        defaultLocale: 'zh-CN',
        locales: ['zh-CN', 'en'],
      },
      localePath: typeof window === 'undefined' ? require('path').resolve('./public/locales') : '/locales',
      reloadOnPrerender: process.env.NODE_ENV === 'development',
    };
    ```
3.  **集成到 `next.config.js`**: 将 `i18n` 配置从 `next-i18next.config.js` 导入并应用。
4.  **集成到 `_app.tsx`**: 使用 `appWithTranslation` HOC (高阶组件) 包裹 `App` 组件，以提供全局的 i18n 上下文。

### 4.2. 在组件中使用

-   **加载命名空间**: 在页面级组件中，通过 `serverSideTranslations` 在 `getStaticProps` 或 `getServerSideProps` 中加载所需的命名空间。
    ```typescript
    import { serverSideTranslations } from 'next-i18next/serverSideTranslations';

    export async function getStaticProps({ locale }) {
      return {
        props: {
          ...(await serverSideTranslations(locale, ['common', 'auth'])),
        },
      };
    }
    ```
-   **使用 `useTranslation` Hook**: 在组件中，使用 `useTranslation` hook 来获取 `t` 函数并进行翻译。
    ```typescript
    import { useTranslation } from 'next-i18next';

    const LoginPage = () => {
      const { t } = useTranslation('auth');

      return (
        <div>
          <h1>{t('loginTitle')}</h1>
          <label>{t('common:label.email')}</label>
          {/* ... */}
        </div>
      );
    };
    ```

---

## 5. 翻译工作流

1.  **提取源文本**: 开发人员在开发过程中，将所有面向用户的硬编码字符串替换为 `t()` 函数调用。
2.  **更新源文件**: 定期（如每周）运行脚本，自动扫描代码库，找出所有新的翻译键，并将其添加到 `zh-CN` 的JSON文件中。
3.  **翻译**: 将 `zh-CN` 目录复制为 `en` 目录，然后将JSON文件发送给专业的翻译人员或社区贡献者进行翻译。
4.  **集成与测试**: 翻译完成后，将新的JSON文件合并到代码库中。QA和母语为英语的测试人员将对新语言版本进行审查，检查是否存在翻译错误、UI布局问题或文化不适应性。
5.  **持续集成**: 这个流程将作为我们持续开发和部署周期的一部分。
