# 前端架构与页面规划

**文档状态**: 草案
**所有者**: 前端负责人

---

## 1. 目的

本文档旨在为 Upbase Web 应用的前端开发提供一个清晰、全面的蓝图。它详细定义了应用的所有核心页面、URL路由结构、每个页面的核心内容组件，以及与后端(Supabase)进行数据交互所需的API接口。本文档是前端团队开发、协作和测试的主要依据。

---

## 2. 技术栈与原则

-   **框架**: Next.js (App Router)
-   **语言**: TypeScript
-   **样式**: Tailwind CSS + Shadcn/ui
-   **状态管理**: Zustand (用于全局状态，如用户信息)、React Query/SWR (用于服务端状态缓存与同步)
-   **路由**: 采用基于文件系统的路由 (`/app` 目录)。

---

## 3. 页面、路由与接口规划

### **第一部分: 认证与引导 (Auth & Onboarding)**

这部分处理用户从未登录到成为正式用户的完整流程。

#### **1. 页面: 注册页 (Sign Up Page)**
-   **路由**: `/signup`
-   **核心内容/组件**:
    -   应用Logo和标语
    -   注册表单 (`<SignUpForm />`): 包含邮箱、密码、确认密码输入框及验证逻辑。
    -   “注册”按钮
    -   “已有账户？登录”链接 (`<Link href="/login">`)
-   **所需接口**:
    -   `POST /auth/v1/signup`: 用户提交表单时调用，用于创建新用户。

#### **2. 页面: 登录页 (Login Page)**
-   **路由**: `/login`
-   **核心内容/组件**:
    -   应用Logo和标语
    -   登录表单 (`<LoginForm />`): 包含邮箱、密码输入框及验证逻辑。
    -   “登录”按钮
    -   “忘记密码？”链接
    -   “还没有账户？注册”链接 (`<Link href="/signup">`)
-   **所需接口**:
    -   `POST /auth/v1/token?grant_type=password`: 用户提交表单时调用，用于用户登录并获取会话。

#### **3. 页面: 初始设置向导 (Onboarding Wizard)**
-   **路由**: `/onboarding` (新用户注册后强制跳转到此页面)
-   **核心内容/组件**:
    -   欢迎语
    -   多步骤表单/问卷 (`<OnboardingWizard />`):
        -   步骤一：收集基本资料 (昵称、年龄)
        -   步骤二：设定主要目标 (戒除色情、自我提升等)
        -   步骤三：选择主要触发因素 (压力大、深夜独处等)
    -   “完成并生成我的计划”按钮
-   **所需接口**:
    -   `PATCH /rest/v1/users?id=eq.{uid}`: 在用户完成向导时，更新其 `profile` 字段。
    -   `POST /rest/v1/recovery_plans`: 根据用户输入，调用后端逻辑（或在前端处理）生成第一个计划。
    -   `POST /rest/v1/plan_tasks`: 批量插入为该计划生成的具体任务。

---

### **第二部分: 核心应用 (Main Application)**

用户登录后与之交互的主要界面。

#### **4. 页面: 仪表盘/主页 (Dashboard)**
-   **路由**: `/dashboard` (登录后的默认页面)
-   **核心内容/组件**:
    -   顶部欢迎语 (`<DashboardHeader />`)
    -   **核心指标卡片 (`<StatsCards />`)**: 
        -   当前连续打卡天数
        -   历史最高连续天数
        -   “正气值”分数
    -   **今日打卡模块 (`<DailyCheckIn />`)**: 显示“打卡”按钮或今日已打卡的状态。
    -   **今日任务列表 (`<TodayTasks />`)**: 显示当天需要完成的 `plan_tasks`。
    -   情绪/状态趋势图 (`<MoodChart />`)
-   **所需接口**:
    -   `GET /rest/v1/users?select=profile,positive_energy_score&id=eq.{uid}`: 获取用户昵称和正气值。
    -   `GET /rest/v1/check_in_records?user_id=eq.{uid}&order=check_in_date.desc`: 获取打卡历史，用于计算连续天数和渲染图表。
    -   `GET /rest/v1/plan_tasks?plan_id=eq.{current_plan_id}&task_date=eq.{today}`: 获取今日任务。
    -   `POST /rest/v1/check_in_records`: 用户点击打卡时调用。
    -   `PATCH /rest/v1/plan_tasks?id=eq.{task_id}`: 用户完成一个任务时调用。

#### **5. 页面: 进度与统计页 (Progress & Analytics)**
-   **路由**: `/progress`
-   **核心内容/组件**:
    -   **打卡日历 (`<CheckInCalendar />`)**: 一个完整的日历视图，标记了每天的成功/失败状态。
    -   **数据分析图表 (`<AnalyticsCharts />`)**: 
        -   破戒触发因素分布饼图。
        -   情绪水平变化折线图。
        -   月度成功率柱状图。
-   **所需接口**:
    -   `GET /rest/v1/check_in_records?user_id=eq.{uid}&check_in_date=gte.{start_date}&check_in_date=lte.{end_date}`: 获取指定时间范围内的所有打卡记录，用于渲染日历和所有图表。

#### **6. 页面: 计划详情页 (Plan Details)**
-   **路由**: `/plan`
-   **核心内容/组件**:
    -   当前计划的标题和总体进度 (`<PlanHeader />`)。
    -   未来7天或整个计划周期的任务列表视图 (`<PlanTaskList />`)。
    -   “编辑计划”或“创建新计划”的入口。
-   **所需接口**:
    -   `GET /rest/v1/recovery_plans?user_id=eq.{uid}&status=eq.active`: 获取当前激活的计划。
    -   `GET /rest/v1/plan_tasks?plan_id=eq.{current_plan_id}&order=task_date.asc`: 获取该计划下的所有任务。

---

### **第三部分: 社区 (Community)**

#### **7. 页面: 社区首页/帖子列表 (Community Feed)**
-   **路由**: `/community`
-   **核心内容/组件**:
    -   帖子排序/筛选器 (`<FeedFilter />`)
    -   帖子列表 (`<PostList />`): 滚动加载，每个 `<PostCard />` 显示作者匿名信息、内容摘要、点赞数、评论数。
    -   “创建新帖子”悬浮按钮 (`<CreatePostButton />`)
-   **所需接口**:
    -   `GET /rest/v1/posts?select=*,users(profile)&order=created_at.desc`: 获取帖子列表，并带上作者的部分公开信息（如头像、昵称）。
    -   `GET /rest/v1/posts?rpc=get_hot_posts`: (可选RPC) 获取热帖。

#### **8. 页面: 帖子详情页 (Post Details)**
-   **路由**: `/community/[postId]`
-   **核心内容/组件**:
    -   完整的帖子内容 (`<PostView />`)。
    -   点赞按钮 (`<LikeButton />`)。
    -   评论列表 (`<CommentList />`)。
    -   评论输入框和“提交”按钮 (`<CommentForm />`)。
-   **所需接口**:
    -   `GET /rest/v1/posts?id=eq.{postId}&select=*,users(profile)`: 获取帖子的详细内容。
    -   `GET /rest/v1/comments?post_id=eq.{postId}&select=*,users(profile)&order=created_at.asc`: 获取该帖子的所有评论。
    -   `POST /rest/v1/comments`: 用户提交新评论时调用。
    -   `POST /rest/v1/post_likes`: 用户点赞时调用。
    -   `DELETE /rest/v1/post_likes`: 用户取消点赞时调用。

---

### **第四部分: 设置与其他 (Settings & Others)**

#### **9. 页面: 设置中心 (Settings Hub)**
-   **路由**: `/settings`
-   **核心内容/组件**:
    -   导航列表 (`<SettingsNav />`)，链接到各个子设置页面:
        -   个人资料 (`/settings/profile`)
        -   账户安全 (`/settings/account`)
        -   监督伙伴 (`/settings/partners`)
        -   紧急联系人 (`/settings/emergency-contacts`)
        -   黑名单管理 (`/settings/blocklist`)
-   **所需接口**: 无，仅为导航页面。

#### **10. 页面: 个人资料设置页 (Profile Settings)**
-   **路由**: `/settings/profile`
-   **核心内容/组件**:
    -   编辑表单 (`<ProfileForm />`): 包含昵称、年龄、目标等字段。
    -   “保存更改”按钮。
-   **所需接口**:
    -   `GET /rest/v1/users?select=profile&id=eq.{uid}`: 加载页面时，获取当前用户的资料以填充表单。
    -   `PATCH /rest/v1/users?id=eq.{uid}`: 用户提交表单时调用。

#### **11. 页面: 监督伙伴管理页 (Accountability Partners)**
-   **路由**: `/settings/partners`
-   **核心内容/组件**:
    -   显示当前伙伴关系的状态 (`<PartnerStatus />`)。
    -   邀请表单 (`<PartnerInviteForm />`): 包含输入框和“发送邀请”按钮。
    -   “解除关系”按钮。
-   **所需接口**:
    -   `GET /rest/v1/accountability_partners?or=(requester_id.eq.{uid},addressee_id.eq.{uid})`: 获取与我相关的伙伴关系。
    -   `POST /rest/v1/accountability_partners`: 发送邀请时调用。
    -   `PATCH /rest/v1/accountability_partners?id=eq.{partnershipId}`: 接受/拒绝邀请或终止关系时调用。
