'use client'

import { useState } from 'react'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { mockSupabase } from '@/lib/supabase/client'
import { isValidEmail } from '@/lib/utils'

export default function ForgotPasswordPage() {
  const [email, setEmail] = useState('')
  const [loading, setLoading] = useState(false)
  const [message, setMessage] = useState('')
  const [error, setError] = useState('')

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError('')
    setMessage('')
    setLoading(true)

    if (!isValidEmail(email)) {
      setError('请输入有效的邮箱地址')
      setLoading(false)
      return
    }

    try {
      // 在实际应用中，这里会调用 Supabase 的真实方法
      const { error: authError } = await mockSupabase.auth.resetPasswordForEmail(email)

      if (authError) {
        setError(authError.message)
      } else {
        setMessage('如果邮箱地址存在，您将会收到一封密码重置邮件。')
      }
    } catch (err) {
      setError('操作失败，请稍后重试')
    } finally {
      setLoading(false)
    }
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>忘记密码</CardTitle>
        <CardDescription>
          输入您的邮箱地址，我们将向您发送重置密码的链接。
        </CardDescription>
      </CardHeader>
      <form onSubmit={handleSubmit}>
        <CardContent className="space-y-4">
          {error && (
            <div className="p-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">
              {error}
            </div>
          )}
          {message && (
            <div className="p-3 text-sm text-green-600 bg-green-50 border border-green-200 rounded-md">
              {message}
            </div>
          )}
          
          <div className="space-y-2">
            <label htmlFor="email" className="text-sm font-medium">
              邮箱
            </label>
            <Input
              id="email"
              type="email"
              placeholder="请输入您的邮箱"
              value={email}
              onChange={(e) => setEmail(e.targe.value)}
              disabled={loading}
            />
          </div>
        </CardContent>
        
        <CardFooter className="flex flex-col space-y-4">
          <Button 
            type="submit" 
            className="w-full" 
            loading={loading}
            variant="gradient"
          >
            发送重置链接
          </Button>
          
          <div className="text-center text-sm text-gray-600">
            记起密码了？{' '}
            <Link href="/login" className="text-blue-600 hover:underline">
              返回登录
            </Link>
          </div>
        </CardFooter>
      </form>
    </Card>
  )
}
