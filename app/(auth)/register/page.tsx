'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { useRouter, useSearchParams } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { mockSupabase } from '@/lib/supabase/client'
import { isValidEmail, validatePassword } from '@/lib/utils'

export default function RegisterPage() {
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    confirmPassword: '',
    username: '',
    inviteCode: ''
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const router = useRouter()
  const searchParams = useSearchParams()

  // 从URL参数获取邀请码
  useEffect(() => {
    const inviteFromUrl = searchParams.get('invite')
    if (inviteFromUrl) {
      setFormData(prev => ({
        ...prev,
        inviteCode: inviteFromUrl
      }))
    }
  }, [searchParams])

  const handleChange = (field: string) => (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({
      ...prev,
      [field]: e.target.value
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError('')
    setLoading(true)

    // 基本验证
    if (!formData.email || !formData.password || !formData.confirmPassword || !formData.username) {
      setError('请填写所有字段')
      setLoading(false)
      return
    }

    if (!isValidEmail(formData.email)) {
      setError('请输入有效的邮箱地址')
      setLoading(false)
      return
    }

    const passwordValidation = validatePassword(formData.password)
    if (!passwordValidation.isValid) {
      setError(passwordValidation.message)
      setLoading(false)
      return
    }

    if (formData.password !== formData.confirmPassword) {
      setError('两次输入的密码不一致')
      setLoading(false)
      return
    }

    if (formData.username.length < 2) {
      setError('用户名至少2个字符')
      setLoading(false)
      return
    }

    try {
      // 使用模拟的 Supabase 客户端
      const { data, error: authError } = await mockSupabase.auth.signUp({
        email: formData.email,
        password: formData.password,
      })

      if (authError) {
        setError(authError.message)
        return
      }

      if (data.user) {
        // 保存模拟会话到 localStorage
        localStorage.setItem('mock-session', JSON.stringify(data.session))
        localStorage.setItem('mock-user', JSON.stringify({
          ...data.user,
          username: formData.username
        }))
        
        // 跳转到引导页面
        router.push('/onboarding')
      }
    } catch (err) {
      setError('注册失败，请稍后重试')
    } finally {
      setLoading(false)
    }
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>注册</CardTitle>
        <CardDescription>
          创建您的账户，开始健康的生活方式
        </CardDescription>
      </CardHeader>
      <form onSubmit={handleSubmit}>
        <CardContent className="space-y-4">
          {error && (
            <div className="p-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">
              {error}
            </div>
          )}
          
          <div className="space-y-2">
            <label htmlFor="username" className="text-sm font-medium">
              用户名
            </label>
            <Input
              id="username"
              type="text"
              placeholder="请输入用户名"
              value={formData.username}
              onChange={handleChange('username')}
              disabled={loading}
            />
          </div>
          
          <div className="space-y-2">
            <label htmlFor="email" className="text-sm font-medium">
              邮箱
            </label>
            <Input
              id="email"
              type="email"
              placeholder="请输入邮箱"
              value={formData.email}
              onChange={handleChange('email')}
              disabled={loading}
            />
          </div>
          
          <div className="space-y-2">
            <label htmlFor="password" className="text-sm font-medium">
              密码
            </label>
            <Input
              id="password"
              type="password"
              placeholder="请输入密码"
              value={formData.password}
              onChange={handleChange('password')}
              disabled={loading}
            />
            <p className="text-xs text-gray-500">
              密码需包含大小写字母和数字，至少8位
            </p>
          </div>
          
          <div className="space-y-2">
            <label htmlFor="confirmPassword" className="text-sm font-medium">
              确认密码
            </label>
            <Input
              id="confirmPassword"
              type="password"
              placeholder="请再次输入密码"
              value={formData.confirmPassword}
              onChange={handleChange('confirmPassword')}
              disabled={loading}
            />
          </div>

          <div className="space-y-2">
            <label htmlFor="inviteCode" className="text-sm font-medium">
              邀请码 <span className="text-gray-400">(选填)</span>
            </label>
            <Input
              id="inviteCode"
              type="text"
              placeholder="请输入邀请码"
              value={formData.inviteCode}
              onChange={handleChange('inviteCode')}
              disabled={loading}
            />
            <p className="text-xs text-gray-500">
              有邀请码可获得额外奖励和特权
            </p>
          </div>
        </CardContent>
        
        <CardFooter className="flex flex-col space-y-4">
          <Button 
            type="submit" 
            className="w-full" 
            loading={loading}
            variant="gradient"
          >
            注册
          </Button>
          
          <div className="text-center text-sm text-gray-600">
            已有账户？{' '}
            <Link href="/login" className="text-blue-600 hover:underline">
              立即登录
            </Link>
          </div>
        </CardFooter>
      </form>
    </Card>
  )
}
