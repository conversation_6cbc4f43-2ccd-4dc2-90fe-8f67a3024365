'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { 
  RefreshCw,
  Users,
  MessageSquare,
  Award,
  TrendingUp,
  Calendar,
  Target,
  Zap,
  Activity
} from 'lucide-react'
import { 
  generateMockPosts, 
  generateMockLeaderboard, 
  generateMockAchievements,
  generateMockCommunityStats,
  generateMockUserActivity,
  mockApiResponse 
} from '@/lib/mock-data'

export default function DataDemoPage() {
  const [posts, setPosts] = useState<any[]>([])
  const [leaderboard, setLeaderboard] = useState<any>(null)
  const [achievements, setAchievements] = useState<any>(null)
  const [communityStats, setCommunityStats] = useState<any>(null)
  const [userActivity, setUserActivity] = useState<any[]>([])
  const [loading, setLoading] = useState(false)

  const generateNewData = async () => {
    setLoading(true)
    try {
      const [
        postsResponse,
        leaderboardResponse,
        achievementsResponse,
        statsResponse,
        activityResponse
      ] = await Promise.all([
        mockApiResponse(generateMockPosts(10)),
        mockApiResponse(generateMockLeaderboard()),
        mockApiResponse(generateMockAchievements()),
        mockApiResponse(generateMockCommunityStats()),
        mockApiResponse(generateMockUserActivity(15))
      ])

      setPosts(postsResponse.data)
      setLeaderboard(leaderboardResponse.data)
      setAchievements(achievementsResponse.data)
      setCommunityStats(statsResponse.data)
      setUserActivity(activityResponse.data)
    } catch (error) {
      console.error('生成数据失败:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    generateNewData()
  }, [])

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">虚拟数据展示</h1>
          <p className="text-gray-600">展示动态生成的虚拟数据，每次刷新都会产生新的内容</p>
        </div>
        <Button 
          onClick={generateNewData} 
          disabled={loading}
          variant="gradient"
        >
          <RefreshCw className={`mr-2 h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
          {loading ? '生成中...' : '重新生成'}
        </Button>
      </div>

      {/* 社区统计概览 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">活跃用户</CardTitle>
            <Users className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{communityStats?.activeUsers?.toLocaleString() || '0'}</div>
            <p className="text-xs text-muted-foreground">
              本周新增 +{communityStats?.weeklyGrowth || 0}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">今日帖子</CardTitle>
            <MessageSquare className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{communityStats?.todayPosts || 0}</div>
            <p className="text-xs text-muted-foreground">
              评论 {communityStats?.todayComments || 0} 条
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总互动</CardTitle>
            <TrendingUp className="h-4 w-4 text-purple-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{communityStats?.totalInteractions?.toLocaleString() || '0'}</div>
            <p className="text-xs text-muted-foreground">
              点赞和评论
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总用户</CardTitle>
            <Target className="h-4 w-4 text-orange-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{communityStats?.totalUsers?.toLocaleString() || '0'}</div>
            <p className="text-xs text-muted-foreground">
              持续增长中
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 热门标签 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Zap className="mr-2 h-5 w-5" />
            热门标签
          </CardTitle>
          <CardDescription>
            社区中最受欢迎的话题标签
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            {communityStats?.popularTags?.map((tag: any, index: number) => (
              <div
                key={index}
                className="px-3 py-1 bg-gradient-to-r from-blue-100 to-purple-100 text-blue-800 rounded-full text-sm font-medium"
              >
                #{tag.tag} ({tag.count})
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 最新帖子 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <MessageSquare className="mr-2 h-5 w-5" />
              最新帖子
            </CardTitle>
            <CardDescription>
              社区中最新发布的帖子
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {posts.slice(0, 5).map((post: any) => (
                <div key={post.id} className="p-3 border rounded-lg hover:bg-gray-50 transition-colors">
                  <div className="flex items-start justify-between mb-2">
                    <h4 className="font-medium text-sm line-clamp-1">{post.title}</h4>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      post.type === 'share' ? 'bg-green-100 text-green-800' :
                      post.type === 'question' ? 'bg-blue-100 text-blue-800' :
                      'bg-purple-100 text-purple-800'
                    }`}>
                      {post.type === 'share' ? '分享' : 
                       post.type === 'question' ? '求助' : '支持'}
                    </span>
                  </div>
                  <p className="text-xs text-gray-600 line-clamp-2 mb-2">
                    {post.content}
                  </p>
                  <div className="flex items-center justify-between text-xs text-gray-500">
                    <span>{post.anonymous_id}</span>
                    <div className="flex items-center gap-3">
                      <span>👍 {post.likes_count}</span>
                      <span>💬 {post.comments_count}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* 排行榜预览 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Award className="mr-2 h-5 w-5" />
              排行榜
            </CardTitle>
            <CardDescription>
              连续天数和贡献排行
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h4 className="font-medium text-sm mb-2 text-orange-600">🔥 连续天数 TOP 3</h4>
                <div className="space-y-2">
                  {leaderboard?.streakLeaderboard?.slice(0, 3).map((user: any, index: number) => (
                    <div key={index} className="flex items-center justify-between text-sm">
                      <div className="flex items-center gap-2">
                        <span className={`w-5 h-5 rounded-full flex items-center justify-center text-xs font-bold text-white ${
                          index === 0 ? 'bg-yellow-500' :
                          index === 1 ? 'bg-gray-500' : 'bg-orange-500'
                        }`}>
                          {index + 1}
                        </span>
                        <span>{user.name}</span>
                      </div>
                      <span className="font-medium text-orange-600">{user.days}天</span>
                    </div>
                  ))}
                </div>
              </div>
              
              <div>
                <h4 className="font-medium text-sm mb-2 text-red-600">❤️ 贡献排行 TOP 3</h4>
                <div className="space-y-2">
                  {leaderboard?.contributionLeaderboard?.slice(0, 3).map((user: any, index: number) => (
                    <div key={index} className="flex items-center justify-between text-sm">
                      <div className="flex items-center gap-2">
                        <span className={`w-5 h-5 rounded-full flex items-center justify-center text-xs font-bold text-white ${
                          index === 0 ? 'bg-yellow-500' :
                          index === 1 ? 'bg-gray-500' : 'bg-orange-500'
                        }`}>
                          {index + 1}
                        </span>
                        <span>{user.name}</span>
                      </div>
                      <span className="font-medium text-red-600">{user.points}分</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 用户活动时间线 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Activity className="mr-2 h-5 w-5" />
            用户活动时间线
          </CardTitle>
          <CardDescription>
            最近的用户活动记录
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {userActivity.slice(0, 8).map((activity: any) => (
              <div key={activity.id} className="flex items-center gap-3 p-2 rounded-lg hover:bg-gray-50">
                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <Calendar className="h-4 w-4 text-blue-600" />
                </div>
                <div className="flex-1">
                  <p className="text-sm font-medium">{activity.label}</p>
                  <p className="text-xs text-gray-500">{activity.description}</p>
                </div>
                <div className="text-xs text-gray-400">
                  +{activity.points}分
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
