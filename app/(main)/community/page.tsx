'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  MessageSquare,
  MessageCircle,
  Heart,
  Users,
  Plus,
  Search,
  Filter,
  Clock,
  ThumbsUp,
  Award,
  Star,
  Crown,
  Medal,
  Trophy,
  Flame,
  Shield,
  Target,
  Edit3,
  X,
  Share,
  Eye
} from 'lucide-react'
import PostComposer from '@/components/community/post-composer'
import {
  generateMockPosts,
  generateMockLeaderboard,
  generateMockAchievements,
  generateMockCommunityStats,
  mockApiResponse
} from '@/lib/mock-data'
import { formatRelativeDate } from '@/lib/utils'
import type { Post } from '@/types'
import Link from 'next/link'

export default function CommunityPage() {
  const [posts, setPosts] = useState<Post[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedFilter, setSelectedFilter] = useState<'all' | 'share' | 'question' | 'support'>('all')
  const [showAchievements, setShowAchievements] = useState(false)
  const [activeTab, setActiveTab] = useState<'posts' | 'leaderboard' | 'achievements'>('posts')
  const [leaderboardData, setLeaderboardData] = useState<any>(null)
  const [achievementsData, setAchievementsData] = useState<any>(null)
  const [communityStats, setCommunityStats] = useState<any>(null)
  const [showPostComposer, setShowPostComposer] = useState(false)

  useEffect(() => {
    loadCommunityData()
  }, [])

  const handleNewPost = (postData: any) => {
    // 将新帖子添加到列表顶部
    const newPost: Post = {
      ...postData,
      author: {
        id: 'current-user',
        name: postData.is_anonymous ? '匿名用户' : '当前用户',
        avatar: postData.is_anonymous ? '' : 'https://api.dicebear.com/7.x/avataaars/svg?seed=current',
        level: 5,
        streak_days: 23,
        is_verified: !postData.is_anonymous
      },
      engagement: {
        likes: 0,
        comments: 0,
        shares: 0,
        views: 1
      }
    }

    setPosts([newPost, ...posts])
    setShowPostComposer(false)

    // 这里可以添加成功提示
    console.log('新帖子发布成功:', newPost)
  }

  const loadCommunityData = async () => {
    try {
      const [postsResponse, leaderboardResponse, achievementsResponse, statsResponse] = await Promise.all([
        mockApiResponse(generateMockPosts(25)),
        mockApiResponse(generateMockLeaderboard()),
        mockApiResponse(generateMockAchievements()),
        mockApiResponse(generateMockCommunityStats())
      ])

      setPosts(postsResponse.data)
      setLeaderboardData(leaderboardResponse.data)
      setAchievementsData(achievementsResponse.data)
      setCommunityStats(statsResponse.data)
    } catch (error) {
      console.error('加载社区数据失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleLike = async (postId: string) => {
    try {
      await mockApiResponse({ success: true })
      setPosts(prev => 
        prev.map(post => 
          post.id === postId 
            ? { ...post, likes_count: post.likes_count + 1 }
            : post
        )
      )
    } catch (error) {
      console.error('点赞失败:', error)
    }
  }

  const filteredPosts = posts.filter(post => {
    const matchesSearch = post.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         post.content.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesFilter = selectedFilter === 'all' || post.type === selectedFilter
    return matchesSearch && matchesFilter
  })

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'share': return '经验分享'
      case 'question': return '求助提问'
      case 'support': return '相互支持'
      default: return '其他'
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'share': return 'bg-green-100 text-green-800'
      case 'question': return 'bg-blue-100 text-blue-800'
      case 'support': return 'bg-purple-100 text-purple-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 rounded-lg"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">戒友社区</h1>
          <p className="text-gray-600">与志同道合的朋友分享经验，互相支持</p>
        </div>
        <Button
          variant="gradient"
          className="mt-4 sm:mt-0"
          onClick={() => setShowPostComposer(!showPostComposer)}
        >
          {showPostComposer ? (
            <>
              <X className="mr-2 h-4 w-4" />
              取消发布
            </>
          ) : (
            <>
              <Edit3 className="mr-2 h-4 w-4" />
              发布动态
            </>
          )}
        </Button>
      </div>

      {/* 搜索和筛选 */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="搜索帖子..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            <div className="flex gap-2">
              {[
                { key: 'all', label: '全部' },
                { key: 'share', label: '分享' },
                { key: 'question', label: '求助' },
                { key: 'support', label: '支持' }
              ].map(filter => (
                <Button
                  key={filter.key}
                  variant={selectedFilter === filter.key ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setSelectedFilter(filter.key as any)}
                >
                  {filter.label}
                </Button>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 发布动态组件 */}
      {showPostComposer && (
        <div className="animate-in slide-in-from-top-2 duration-300">
          <PostComposer
            onPost={handleNewPost}
            onCancel={() => setShowPostComposer(false)}
          />
        </div>
      )}

      {/* 社区统计 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">活跃用户</CardTitle>
            <Users className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{communityStats?.activeUsers?.toLocaleString() || '1,234'}</div>
            <p className="text-xs text-muted-foreground">
              本周新增 +{communityStats?.weeklyGrowth || 23}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">今日帖子</CardTitle>
            <MessageSquare className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{communityStats?.todayPosts || 42}</div>
            <p className="text-xs text-muted-foreground">
              评论 {communityStats?.todayComments || 156} 条
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">互动次数</CardTitle>
            <Heart className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{communityStats?.totalInteractions?.toLocaleString() || '856'}</div>
            <p className="text-xs text-muted-foreground">
              点赞和评论
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总用户数</CardTitle>
            <Trophy className="h-4 w-4 text-yellow-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{communityStats?.totalUsers?.toLocaleString() || '5,678'}</div>
            <p className="text-xs text-muted-foreground">
              持续增长中
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 快速导航 */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card className="hover:shadow-md transition-shadow cursor-pointer" onClick={() => window.location.href = '/community/buddy-system'}>
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                <Users className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <h3 className="font-medium text-gray-900">戒友配对</h3>
                <p className="text-sm text-gray-500">找到志同道合的戒友，互相监督成长</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-md transition-shadow cursor-pointer" onClick={() => window.location.href = '/community/messages'}>
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                <MessageCircle className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <h3 className="font-medium text-gray-900">私信系统</h3>
                <p className="text-sm text-gray-500">与戒友私下交流，分享经验和心得</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 标签页导航 */}
      <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg w-fit">
        <Button
          variant={activeTab === 'posts' ? 'default' : 'ghost'}
          size="sm"
          onClick={() => setActiveTab('posts')}
        >
          <MessageSquare className="mr-2 h-4 w-4" />
          帖子动态
        </Button>
        <Button
          variant={activeTab === 'leaderboard' ? 'default' : 'ghost'}
          size="sm"
          onClick={() => setActiveTab('leaderboard')}
        >
          <Trophy className="mr-2 h-4 w-4" />
          排行榜
        </Button>
        <Button
          variant={activeTab === 'achievements' ? 'default' : 'ghost'}
          size="sm"
          onClick={() => setActiveTab('achievements')}
        >
          <Award className="mr-2 h-4 w-4" />
          成就系统
        </Button>
      </div>

      {/* 内容区域 */}
      {activeTab === 'posts' && (
        <>
          {/* 帖子列表 */}
      <div className="space-y-4">
        {filteredPosts.map((post) => (
          <Card key={post.id} className="hover:shadow-md transition-shadow">
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-sm font-medium">
                      {post.anonymous_id[0]}
                    </div>
                    <div>
                      <>
                      <p className="font-medium text-sm">{post.anonymous_id}</p>
                      <p className="text-xs text-gray-500 flex items-center">
                        <Clock className="mr-1 h-3 w-3" />
                        {formatRelativeDate(post.created_at)}
                      </p>
                    </>
                    </div>
                  </div>
                  <CardTitle className="text-lg">
                    <Link href={`/article/${post.id}`} className="hover:underline">
                      {post.title}
                    </Link>
                  </CardTitle>
                </div>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getTypeColor(post.type)}`}>
                  {getTypeLabel(post.type)}
                </span>
              </div>
            </CardHeader>
            <CardContent>
              <>
                <p className="text-gray-700 mb-4 line-clamp-3">
                  {post.content}
                </p>
                <Link href={`/article/${post.id}`} className="text-sm font-medium text-blue-600 hover:underline">
                  阅读全文
                </Link>
              </>
              
              {post.tags && post.tags.length > 0 && (
                <div className="flex flex-wrap gap-1 mb-4">
                  {post.tags.map((tag, index) => (
                    <span
                      key={index}
                      className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded"
                    >
                      #{tag}
                    </span>
                  ))}
                </div>
              )}
              
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleLike(post.id)}
                    className="text-gray-600 hover:text-red-500"
                  >
                    <ThumbsUp className="mr-1 h-4 w-4" />
                    {post.likes_count}
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-gray-600 hover:text-blue-500"
                  >
                    <MessageSquare className="mr-1 h-4 w-4" />
                    {post.comments_count}
                  </Button>
                </div>
                <Link href={`/article/${post.id}`}>
                  <Button size="sm">
                    查看详情
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
            )}
            loadMore={loadMorePosts}
            hasMore={hasMore}
            loading={loading}
            pageSize={5}
            enablePullToRefresh={true}
            onRefresh={handleRefresh}
            className="space-y-4"
          />
        </>
      )}

      {/* 排行榜 */}
      {activeTab === 'leaderboard' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 连续天数排行榜 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Flame className="mr-2 h-5 w-5 text-orange-500" />
                连续天数排行榜
              </CardTitle>
              <CardDescription>
                本月连续戒色天数最多的戒友
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {(leaderboardData?.streakLeaderboard?.slice(0, 10) || [
                  { rank: 1, name: '坚持者A', days: 45, icon: Crown, color: 'text-yellow-500' },
                  { rank: 2, name: '努力者B', days: 38, icon: Medal, color: 'text-gray-400' },
                  { rank: 3, name: '奋斗者C', days: 32, icon: Award, color: 'text-orange-500' },
                  { rank: 4, name: '追梦者D', days: 28, icon: Star, color: 'text-blue-500' },
                  { rank: 5, name: '勇敢者E', days: 25, icon: Target, color: 'text-green-500' }
                ]).map((user: any) => {
                  const IconComponent = user.rank === 1 ? Crown :
                                      user.rank === 2 ? Medal :
                                      user.rank === 3 ? Award :
                                      user.rank <= 5 ? Star : Target
                  const iconColor = user.rank === 1 ? 'text-yellow-500' :
                                   user.rank === 2 ? 'text-gray-400' :
                                   user.rank === 3 ? 'text-orange-500' :
                                   user.rank <= 5 ? 'text-blue-500' : 'text-green-500'

                  return (
                    <div key={user.rank} className="flex items-center justify-between p-3 rounded-lg bg-gray-50">
                      <div className="flex items-center gap-3">
                        <div className={`flex items-center justify-center w-8 h-8 rounded-full ${
                          user.rank === 1 ? 'bg-yellow-100' :
                          user.rank === 2 ? 'bg-gray-100' :
                          user.rank === 3 ? 'bg-orange-100' : 'bg-blue-100'
                        }`}>
                          <IconComponent className={`h-4 w-4 ${iconColor}`} />
                        </div>
                        <div>
                          <p className="font-medium text-sm">{user.name}</p>
                          <p className="text-xs text-gray-500">第 {user.rank} 名</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-bold text-lg text-orange-600">{user.days}</p>
                        <p className="text-xs text-gray-500">天</p>
                      </div>
                    </div>
                  )
                })}
              </div>
            </CardContent>
          </Card>

          {/* 社区贡献排行榜 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Heart className="mr-2 h-5 w-5 text-red-500" />
                社区贡献排行榜
              </CardTitle>
              <CardDescription>
                本月帮助他人最多的热心戒友
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {(leaderboardData?.contributionLeaderboard?.slice(0, 10) || [
                  { rank: 1, name: '热心者A', points: 1250, posts: 15, likes: 89 },
                  { rank: 2, name: '助人者B', points: 980, posts: 12, likes: 67 },
                  { rank: 3, name: '分享者C', points: 756, posts: 9, likes: 54 },
                  { rank: 4, name: '支持者D', points: 623, posts: 8, likes: 43 },
                  { rank: 5, name: '鼓励者E', points: 567, posts: 7, likes: 38 }
                ]).map((user: any) => (
                  <div key={user.rank} className="flex items-center justify-between p-3 rounded-lg bg-gray-50">
                    <div className="flex items-center gap-3">
                      <div className={`flex items-center justify-center w-8 h-8 rounded-full font-bold text-white ${
                        user.rank === 1 ? 'bg-yellow-500' :
                        user.rank === 2 ? 'bg-gray-500' :
                        user.rank === 3 ? 'bg-orange-500' : 'bg-blue-500'
                      }`}>
                        {user.rank}
                      </div>
                      <div>
                        <p className="font-medium text-sm">{user.name}</p>
                        <p className="text-xs text-gray-500">{user.posts} 帖子 • {user.likes} 点赞</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-bold text-lg text-red-600">{user.points}</p>
                      <p className="text-xs text-gray-500">贡献值</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* 成就系统 */}
      {activeTab === 'achievements' && (
        <div className="space-y-6">
          {/* 我的成就 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Trophy className="mr-2 h-5 w-5 text-yellow-500" />
                我的成就
              </CardTitle>
              <CardDescription>
                您已获得的成就徽章
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {(achievementsData?.userAchievements?.slice(0, 8) || [
                  {
                    name: '初心不改',
                    description: '连续戒色7天',
                    icon: 'Shield',
                    color: 'bg-blue-500',
                    achieved: true,
                    achievedAt: '2024-01-15'
                  },
                  {
                    name: '坚持不懈',
                    description: '连续戒色30天',
                    icon: 'Target',
                    color: 'bg-green-500',
                    achieved: true,
                    achievedAt: '2024-01-30'
                  },
                  {
                    name: '社区之星',
                    description: '获得100个点赞',
                    icon: 'Star',
                    color: 'bg-yellow-500',
                    achieved: true,
                    achievedAt: '2024-02-05'
                  },
                  {
                    name: '助人为乐',
                    description: '帮助10位戒友',
                    icon: 'Heart',
                    color: 'bg-red-500',
                    achieved: false
                  },
                  {
                    name: '百日筑基',
                    description: '连续戒色100天',
                    icon: 'Crown',
                    color: 'bg-purple-500',
                    achieved: false
                  },
                  {
                    name: '知识分享者',
                    description: '发布5篇优质文章',
                    icon: 'Award',
                    color: 'bg-indigo-500',
                    achieved: false
                  }
                ]).map((achievement: any, index: number) => {
                  const IconComponent = achievement.icon === 'Shield' ? Shield :
                                       achievement.icon === 'Target' ? Target :
                                       achievement.icon === 'Star' ? Star :
                                       achievement.icon === 'Heart' ? Heart :
                                       achievement.icon === 'Crown' ? Crown :
                                       achievement.icon === 'Award' ? Award : Shield

                  return (
                    <div
                      key={index}
                      className={`p-4 rounded-lg border text-center transition-all ${
                        achievement.achieved
                          ? 'bg-gradient-to-br from-yellow-50 to-orange-50 border-yellow-200'
                          : 'bg-gray-50 border-gray-200 opacity-60'
                      }`}
                    >
                      <div className={`w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-2 ${
                        achievement.achieved ? achievement.color : 'bg-gray-400'
                      }`}>
                        <IconComponent className="h-6 w-6 text-white" />
                      </div>
                      <h4 className={`font-medium text-sm mb-1 ${
                        achievement.achieved ? 'text-gray-900' : 'text-gray-500'
                      }`}>
                        {achievement.name}
                      </h4>
                      <p className="text-xs text-gray-600 mb-2">
                        {achievement.description}
                      </p>
                      {achievement.achieved && achievement.achievedAt && (
                        <p className="text-xs text-yellow-600 font-medium">
                          {new Date(achievement.achievedAt).toLocaleDateString()}
                        </p>
                      )}
                      {!achievement.achieved && (
                        <p className="text-xs text-gray-400">
                          未获得
                        </p>
                      )}
                    </div>
                  )
                })}
              </div>
            </CardContent>
          </Card>

          {/* 成就进度 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Medal className="mr-2 h-5 w-5 text-orange-500" />
                成就进度
              </CardTitle>
              <CardDescription>
                正在努力获得的成就
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {(achievementsData?.userAchievements?.filter((a: any) => !a.achieved)?.slice(0, 5) || [
                  {
                    name: '助人为乐',
                    description: '帮助10位戒友',
                    progress: 6,
                    requirement: 10
                  },
                  {
                    name: '百日筑基',
                    description: '连续戒色100天',
                    progress: 45,
                    requirement: 100
                  },
                  {
                    name: '知识分享者',
                    description: '发布5篇优质文章',
                    progress: 2,
                    requirement: 5
                  }
                ]).map((achievement: any, index: number) => {
                  const progressPercent = Math.min((achievement.progress / achievement.requirement) * 100, 100)

                  return (
                    <div key={index} className="p-4 rounded-lg bg-gray-50">
                      <div className="flex justify-between items-center mb-2">
                        <h4 className="font-medium text-sm">{achievement.name}</h4>
                        <span className="text-sm text-gray-600">
                          {achievement.progress}/{achievement.requirement}
                        </span>
                      </div>
                      <p className="text-xs text-gray-600 mb-3">
                        {achievement.description}
                      </p>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${progressPercent}%` }}
                        ></div>
                      </div>
                      <div className="text-right text-xs text-gray-500 mt-1">
                        {Math.round(progressPercent)}%
                      </div>
                    </div>
                  )
                })}
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}
