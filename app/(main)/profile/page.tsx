'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { 
  User,
  Calendar,
  Award,
  Target,
  TrendingUp,
  Edit,
  Settings,
  Trophy,
  Flame,
  Star
} from 'lucide-react'
import { mockUser, generateMockUserStats, mockMilestones, mockApiResponse } from '@/lib/mock-data'
import { formatDate, formatRelativeDate } from '@/lib/utils'
import type { User as UserType, UserStats, Milestone } from '@/types'

export default function ProfilePage() {
  const [user, setUser] = useState<UserType | null>(null)
  const [stats, setStats] = useState<UserStats | null>(null)
  const [milestones, setMilestones] = useState<Milestone[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadProfileData()
  }, [])

  const loadProfileData = async () => {
    try {
      const [userResponse, statsResponse, milestonesResponse] = await Promise.all([
        mockApiResponse(mockUser),
        mockApiResponse(generateMockUserStats()),
        mockApiResponse(mockMilestones)
      ])

      setUser(userResponse.data)
      setStats(statsResponse.data)
      setMilestones(milestonesResponse.data)
    } catch (error) {
      console.error('加载个人资料失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const getGoalLabel = (goal?: string) => {
    switch (goal) {
      case 'quit_porn': return '戒除色情'
      case 'reduce_frequency': return '减少频率'
      case 'self_improvement': return '自我提升'
      default: return '未设置'
    }
  }

  const getHistoryLabel = (history?: string) => {
    switch (history) {
      case 'beginner': return '新手'
      case 'tried_failed': return '尝试过但失败'
      case 'experienced': return '有一定经验'
      default: return '未设置'
    }
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="h-96 bg-gray-200 rounded-lg"></div>
            <div className="lg:col-span-2 h-96 bg-gray-200 rounded-lg"></div>
          </div>
        </div>
      </div>
    )
  }

  const achievedMilestones = milestones.filter(m => m.achieved)
  const nextMilestone = milestones.find(m => !m.achieved)

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">个人中心</h1>
          <p className="text-gray-600">管理您的个人信息和成就</p>
        </div>
        <Button variant="outline">
          <Edit className="mr-2 h-4 w-4" />
          编辑资料
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 个人信息卡片 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <User className="mr-2 h-5 w-5" />
              个人信息
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-center">
              <div className="w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-2xl font-bold mx-auto mb-4">
                {user?.username?.[0] || user?.email[0].toUpperCase()}
              </div>
              <h3 className="text-lg font-semibold">{user?.username || '用户'}</h3>
              <p className="text-gray-600 text-sm">{user?.email}</p>
            </div>

            <div className="space-y-3 pt-4 border-t">
              <div className="flex justify-between">
                <span className="text-gray-600">年龄</span>
                <span className="font-medium">{user?.profile?.age || '未设置'}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">目标</span>
                <span className="font-medium">{getGoalLabel(user?.profile?.goal)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">经验</span>
                <span className="font-medium">{getHistoryLabel(user?.profile?.history)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">加入时间</span>
                <span className="font-medium">{formatDate(user?.created_at || '', 'yyyy-MM-dd')}</span>
              </div>
            </div>

            <div className="pt-4 border-t">
              <div className="flex justify-between items-center mb-2">
                <span className="text-gray-600">正气值</span>
                <span className="font-bold text-yellow-600">
                  {user?.profile?.positive_energy_score || 0}
                </span>
              </div>
              <Progress value={user?.profile?.positive_energy_score || 0} className="h-2" />
            </div>
          </CardContent>
        </Card>

        {/* 统计数据和成就 */}
        <div className="lg:col-span-2 space-y-6">
          {/* 核心统计 */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">连续天数</CardTitle>
                <Flame className="h-4 w-4 text-orange-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats?.current_streak || 0}</div>
                <p className="text-xs text-muted-foreground">
                  最高: {stats?.longest_streak || 0} 天
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">总天数</CardTitle>
                <Calendar className="h-4 w-4 text-blue-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats?.total_days || 0}</div>
                <p className="text-xs text-muted-foreground">
                  成功率: {stats?.success_rate || 0}%
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">成就数量</CardTitle>
                <Trophy className="h-4 w-4 text-yellow-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{achievedMilestones.length}</div>
                <p className="text-xs text-muted-foreground">
                  共 {milestones.length} 个成就
                </p>
              </CardContent>
            </Card>
          </div>

          {/* 成就系统 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Award className="mr-2 h-5 w-5" />
                成就系统
              </CardTitle>
              <CardDescription>
                您的里程碑和成就记录
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {milestones.map((milestone) => (
                  <div
                    key={milestone.id}
                    className={`flex items-center p-4 rounded-lg border ${
                      milestone.achieved 
                        ? 'bg-green-50 border-green-200' 
                        : 'bg-gray-50 border-gray-200'
                    }`}
                  >
                    <div className={`w-12 h-12 rounded-full flex items-center justify-center mr-4 ${
                      milestone.achieved 
                        ? 'bg-green-500 text-white' 
                        : 'bg-gray-300 text-gray-600'
                    }`}>
                      {milestone.achieved ? (
                        <Trophy className="h-6 w-6" />
                      ) : (
                        <Target className="h-6 w-6" />
                      )}
                    </div>
                    <div className="flex-1">
                      <h4 className={`font-semibold ${
                        milestone.achieved ? 'text-green-700' : 'text-gray-700'
                      }`}>
                        {milestone.title}
                      </h4>
                      <p className="text-sm text-gray-600 mt-1">
                        {milestone.description}
                      </p>
                      <div className="flex items-center mt-2 text-xs text-gray-500">
                        <Target className="mr-1 h-3 w-3" />
                        目标: {milestone.target_days} 天
                        {milestone.achieved && milestone.achieved_at && (
                          <>
                            <span className="mx-2">•</span>
                            <Star className="mr-1 h-3 w-3" />
                            达成于 {formatDate(milestone.achieved_at, 'yyyy-MM-dd')}
                          </>
                        )}
                      </div>
                    </div>
                    {milestone.achieved && (
                      <div className="text-green-500">
                        <Award className="h-6 w-6" />
                      </div>
                    )}
                  </div>
                ))}
              </div>

              {nextMilestone && (
                <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <h4 className="font-semibold text-blue-700 mb-2">下一个目标</h4>
                  <p className="text-blue-600 text-sm mb-3">{nextMilestone.title}</p>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-blue-600">
                      还需 {nextMilestone.target_days - (stats?.current_streak || 0)} 天
                    </span>
                    <span className="text-blue-600">
                      {Math.round(((stats?.current_streak || 0) / nextMilestone.target_days) * 100)}%
                    </span>
                  </div>
                  <Progress 
                    value={((stats?.current_streak || 0) / nextMilestone.target_days) * 100} 
                    className="mt-2 h-2"
                  />
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
