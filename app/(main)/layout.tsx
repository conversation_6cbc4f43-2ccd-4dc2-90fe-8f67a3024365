'use client'

import { useState, useEffect, useRef } from 'react'
import Link from 'next/link'
import { useRouter, usePathname } from 'next/navigation'
import { Button } from '@/components/ui/button'
import {
  Home,
  TrendingUp,
  Users,
  User,
  Settings,
  Menu,
  X,
  LogOut,
  Target,
  Shield,
  BookOpen,
  Database,
  ChevronLeft,
  Sparkles,
  Heart,
  Zap,
  AlertTriangle,
  Calendar,
  Bot
} from 'lucide-react'

const navigation = [
  { name: '仪表盘', href: '/dashboard', icon: Home },
  { name: '我的计划', href: '/plan', icon: Target },
  { name: '每日追踪', href: '/daily-tracking', icon: Calendar },
  { name: 'AI智能助手', href: '/ai-assistant', icon: Bo<PERSON> },
  { name: '防诱惑工具', href: '/tools', icon: Shield },
  { name: '紧急求助', href: '/emergency', icon: AlertTriangle },
  { name: '学习中心', href: '/learn', icon: BookO<PERSON> },
  { name: '正能量库', href: '/inspiration', icon: Zap },
  { name: '进度统计', href: '/progress', icon: TrendingUp },
  { name: '健康监测', href: '/health-monitoring', icon: Heart },
  { name: '社区', href: '/community', icon: Users },
  { name: 'AI推荐', href: '/recommendations', icon: Sparkles },
  { name: '数据展示', href: '/data-demo', icon: Database },
  { name: '个人中心', href: '/profile', icon: User },
  { name: '设置', href: '/settings', icon: Settings },
]

export default function MainLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [isSidebarCollapsed, setSidebarCollapsed] = useState(false)
  const [userMenuOpen, setUserMenuOpen] = useState(false)
  const [showLogoutConfirm, setShowLogoutConfirm] = useState(false)
  const [user, setUser] = useState<any>(null)
  const router = useRouter()
  const pathname = usePathname()
  const userMenuRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const mockUser = localStorage.getItem('mock-user')
    const mockSession = localStorage.getItem('mock-session')
    
    if (!mockUser || !mockSession) {
      router.push('/login')
      return
    }
    
    setUser(JSON.parse(mockUser))
  }, [router])

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (userMenuRef.current && !userMenuRef.current.contains(event.target as Node)) {
        setUserMenuOpen(false)
      }
    }
    document.addEventListener("mousedown", handleClickOutside)
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [userMenuRef])

  const handleLogout = () => {
    setShowLogoutConfirm(true)
  }

  const confirmLogout = () => {
    localStorage.removeItem('mock-user')
    localStorage.removeItem('mock-session')
    router.push('/login')
  }

  const cancelLogout = () => {
    setShowLogoutConfirm(false)
  }

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="flex h-screen bg-gray-50 overflow-hidden">
      {/* Mobile sidebar overlay */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div
        className={`
          ${isSidebarCollapsed ? 'w-20' : 'w-64'}
          ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}
          fixed inset-y-0 left-0 z-50 flex h-full flex-col bg-white shadow-lg
          transition-all duration-300 ease-in-out lg:static lg:translate-x-0
        `}
      >
        {/* Sidebar Header */}
        <div className="flex h-16 flex-shrink-0 items-center justify-between border-b px-4">
          <h1
            className={`text-xl font-bold text-gray-900 whitespace-nowrap transition-opacity duration-300 ${
              isSidebarCollapsed ? 'opacity-0 w-0' : 'opacity-100'
            }`}
          >
            Upbase
          </h1>
          <Button
            variant="ghost"
            size="icon"
            className="hidden lg:flex"
            onClick={() => setSidebarCollapsed(!isSidebarCollapsed)}
          >
            <ChevronLeft
              className={`h-5 w-5 transition-transform duration-300 ${
                isSidebarCollapsed ? 'rotate-180' : ''
              }`}
            />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            className="lg:hidden"
            onClick={() => setSidebarOpen(false)}
          >
            <X className="h-5 w-5" />
          </Button>
        </div>

        {/* Navigation */}
        <nav className="flex-grow overflow-y-auto overflow-x-hidden mt-6 px-3">
          <div className="space-y-1">
            {navigation.map((item) => {
              const Icon = item.icon
              const isActive = pathname === item.href
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  title={isSidebarCollapsed ? item.name : undefined}
                  className={`
                    flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors
                    ${isActive
                      ? 'bg-blue-50 text-blue-700'
                      : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'}
                    ${isSidebarCollapsed ? 'justify-center' : ''}
                  `}
                  onClick={() => setSidebarOpen(false)}
                >
                  <Icon className={`h-5 w-5 flex-shrink-0 ${!isSidebarCollapsed ? 'mr-3' : ''}`} />
                  <span className={`whitespace-nowrap transition-opacity duration-200 ${isSidebarCollapsed ? 'opacity-0 w-0' : 'opacity-100'}`}>{item.name}</span>
                </Link>
              )
            })}
          </div>
        </nav>

        {/* User Info and Logout */}
        <div className="relative flex-shrink-0 border-t" ref={userMenuRef}>
           {/* Pop-up Menu for collapsed sidebar */}
           {isSidebarCollapsed && userMenuOpen && (
            <div className="absolute bottom-full left-2 mb-2 w-60 rounded-md bg-white py-2 shadow-lg ring-1 ring-black ring-opacity-5">
              <div className="px-4 py-2 border-b">
                <p className="text-sm font-medium text-gray-900 truncate">
                  {user.username || '用户'}
                </p>
                <p className="text-xs text-gray-500 truncate">
                  {user.email}
                </p>
              </div>
              <Button
                variant="ghost"
                size="sm"
                className="w-full justify-start text-gray-600 hover:text-gray-900 mt-1 px-4"
                onClick={handleLogout}
              >
                <LogOut className="mr-2 h-4 w-4" />
                退出登录
              </Button>
            </div>
          )}

          {/* User Info Display */}
          <div 
            className={`p-4 flex items-center cursor-pointer ${isSidebarCollapsed ? 'justify-center' : ''}`}
            onClick={() => isSidebarCollapsed && setUserMenuOpen(!userMenuOpen)}
            role={isSidebarCollapsed ? "button" : "div"}
            tabIndex={isSidebarCollapsed ? 0 : -1}
          >
            <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-medium flex-shrink-0">
              {user.username?.[0] || user.email[0].toUpperCase()}
            </div>
            <div className={`ml-3 flex-1 min-w-0 ${isSidebarCollapsed ? 'hidden' : ''}`}>
              <p className="text-sm font-medium text-gray-900 truncate">
                {user.username || '用户'}
              </p>
              <p className="text-xs text-gray-500 truncate">
                {user.email}
              </p>
            </div>
          </div>
          
          {/* Logout Button for expanded sidebar */}
          {!isSidebarCollapsed && (
            <div className="px-4 pb-4">
              <Button
                variant="ghost"
                size="sm"
                className="w-full justify-start text-gray-600 hover:text-gray-900"
                onClick={handleLogout}
              >
                <LogOut className="mr-2 h-4 w-4" />
                退出登录
              </Button>
            </div>
          )}
        </div>
      </div>

      {/* Main Content Area */}
      <div className={`flex flex-1 flex-col overflow-hidden transition-all duration-300 ease-in-out ${isSidebarCollapsed ? 'lg:max-w-[calc(100%-5rem)]' : 'lg:max-w-[calc(100%-16rem)]'}`}>
        {/* Mobile Top Bar */}
        <div className="sticky top-0 z-10 bg-white shadow-sm border-b lg:hidden">
          <div className="flex items-center justify-between h-16 px-4">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setSidebarOpen(true)}
            >
              <Menu className="h-5 w-5" />
            </Button>
            <h1 className="text-lg font-semibold text-gray-900">Upbase</h1>
            <div className="w-10" />
          </div>
        </div>

        {/* Page Content */}
        <main className="flex-1 overflow-y-auto">
          <div className="p-4 lg:p-8">
            {children}
          </div>
        </main>
      </div>

      {/* 退出登录确认对话框 */}
      {showLogoutConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-sm mx-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">确认退出登录</h3>
            <p className="text-gray-600 mb-6">您确定要退出登录吗？退出后需要重新登录才能使用。</p>
            <div className="flex gap-3">
              <button
                onClick={cancelLogout}
                className="flex-1 px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
              >
                取消
              </button>
              <button
                onClick={confirmLogout}
                className="flex-1 px-4 py-2 text-white bg-red-500 rounded-md hover:bg-red-600 transition-colors"
              >
                确认退出
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
