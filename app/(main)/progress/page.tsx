'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import {
  Calendar,
  TrendingUp,
  BarChart3,
  <PERSON><PERSON>hart,
  Award,
  Target,
  Brain,
  Heart,
  Zap,
  Activity,
  Download,
  Filter
} from 'lucide-react'
import { 
  generateMockCheckInRecords, 
  generateMockUserStats,
  mockApiResponse 
} from '@/lib/mock-data'
import { formatDate, calculateStreak, calculateSuccessRate } from '@/lib/utils'
import type { CheckInRecord, UserStats } from '@/types'

export default function ProgressPage() {
  const [checkInRecords, setCheckInRecords] = useState<CheckInRecord[]>([])
  const [stats, setStats] = useState<UserStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [selectedPeriod, setSelectedPeriod] = useState<'week' | 'month' | 'year'>('month')
  const [showRelapseAnalysis, setShowRelapseAnalysis] = useState(false)
  const [selectedMetric, setSelectedMetric] = useState<'streak' | 'mood' | 'energy'>('streak')

  useEffect(() => {
    loadProgressData()
  }, [])

  const loadProgressData = async () => {
    try {
      const [recordsResponse, statsResponse] = await Promise.all([
        mockApiResponse(generateMockCheckInRecords(90)),
        mockApiResponse(generateMockUserStats())
      ])

      setCheckInRecords(recordsResponse.data)
      setStats(statsResponse.data)
    } catch (error) {
      console.error('加载进度数据失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 生成日历数据
  const generateCalendarData = () => {
    const today = new Date()
    const startDate = new Date(today.getFullYear(), today.getMonth(), 1)
    const endDate = new Date(today.getFullYear(), today.getMonth() + 1, 0)
    
    const calendarDays = []
    const currentDate = new Date(startDate)
    
    // 添加月初的空白天数
    const firstDayOfWeek = startDate.getDay()
    for (let i = 0; i < firstDayOfWeek; i++) {
      calendarDays.push(null)
    }
    
    // 添加月份的所有天数
    while (currentDate <= endDate) {
      const dateStr = formatDate(currentDate, 'yyyy-MM-dd')
      const record = checkInRecords.find(r => r.check_in_date === dateStr)
      
      calendarDays.push({
        date: new Date(currentDate),
        dateStr,
        record,
        status: record?.status || null
      })
      
      currentDate.setDate(currentDate.getDate() + 1)
    }
    
    return calendarDays
  }

  const calendarData = generateCalendarData()
  const weekDays = ['日', '一', '二', '三', '四', '五', '六']

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="h-96 bg-gray-200 rounded-lg"></div>
            <div className="h-96 bg-gray-200 rounded-lg"></div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">进度统计</h1>
          <p className="text-gray-600">查看您的详细进度和数据分析</p>
        </div>
        <div className="flex gap-2 mt-4 sm:mt-0">
          <select
            value={selectedPeriod}
            onChange={(e) => setSelectedPeriod(e.target.value as 'week' | 'month' | 'year')}
            className="px-3 py-2 border border-gray-300 rounded-md text-sm"
          >
            <option value="week">本周</option>
            <option value="month">本月</option>
            <option value="year">本年</option>
          </select>
          <Button variant="outline" size="sm">
            <Download className="mr-2 h-4 w-4" />
            导出报告
          </Button>
        </div>
      </div>

      {/* 统计概览 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">当前连续</CardTitle>
            <TrendingUp className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {stats?.current_streak || 0} 天
            </div>
            <p className="text-xs text-muted-foreground">
              历史最高: {stats?.longest_streak || 0} 天
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">成功率</CardTitle>
            <Target className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">
              {stats?.success_rate || 0}%
            </div>
            <p className="text-xs text-muted-foreground">
              总计 {stats?.total_days || 0} 天
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">正气值</CardTitle>
            <Award className="h-4 w-4 text-yellow-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">
              {stats?.positive_energy_score || 0}
            </div>
            <p className="text-xs text-muted-foreground">
              持续提升中
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 打卡日历 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Calendar className="mr-2 h-5 w-5" />
              打卡日历
            </CardTitle>
            <CardDescription>
              {formatDate(new Date(), 'yyyy年MM月')} 的打卡记录
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {/* 星期标题 */}
              <div className="grid grid-cols-7 gap-1">
                {weekDays.map(day => (
                  <div key={day} className="text-center text-sm font-medium text-gray-500 py-2">
                    {day}
                  </div>
                ))}
              </div>
              
              {/* 日历网格 */}
              <div className="grid grid-cols-7 gap-1">
                {calendarData.map((day, index) => (
                  <div
                    key={index}
                    className={`
                      aspect-square flex items-center justify-center text-sm rounded-md
                      ${!day ? '' : 
                        day.status === 'success' ? 'bg-green-100 text-green-800 font-medium' :
                        day.status === 'relapse' ? 'bg-red-100 text-red-800 font-medium' :
                        'bg-gray-50 text-gray-600 hover:bg-gray-100'
                      }
                    `}
                  >
                    {day?.date.getDate()}
                  </div>
                ))}
              </div>
              
              {/* 图例 */}
              <div className="flex items-center justify-center space-x-4 text-xs">
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-green-100 rounded mr-1"></div>
                  <span>成功</span>
                </div>
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-red-100 rounded mr-1"></div>
                  <span>破戒</span>
                </div>
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-gray-100 rounded mr-1"></div>
                  <span>未记录</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 周度进度 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <BarChart3 className="mr-2 h-5 w-5" />
              周度进度
            </CardTitle>
            <CardDescription>
              最近四周的表现统计
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {stats?.weekly_progress.map((week, index) => (
                <div key={index} className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">{week.week}</span>
                    <span className="text-sm text-gray-600">
                      {week.success_days}/{week.total_days} 天
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${week.success_rate}%` }}
                    ></div>
                  </div>
                  <div className="text-right text-xs text-gray-500">
                    {week.success_rate}%
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 情绪趋势 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <PieChart className="mr-2 h-5 w-5" />
            情绪趋势
          </CardTitle>
          <CardDescription>
            最近一周的情绪变化
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-7 gap-2">
            {stats?.mood_trend.map((mood, index) => (
              <div key={index} className="text-center">
                <div className="text-xs text-gray-500 mb-1">
                  {formatDate(mood.date, 'MM/dd')}
                </div>
                <div className={`
                  w-full h-8 rounded flex items-center justify-center text-white text-sm font-medium
                  ${mood.mood >= 4 ? 'bg-green-500' :
                    mood.mood >= 3 ? 'bg-yellow-500' :
                    'bg-red-500'
                  }
                `}>
                  {mood.mood}
                </div>
              </div>
            ))}
          </div>
          <div className="mt-4 text-center text-xs text-gray-500">
            情绪评分：1-很差 2-较差 3-一般 4-良好 5-很好
          </div>
        </CardContent>
      </Card>

      {/* 破戒原因分析 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Brain className="mr-2 h-5 w-5" />
              破戒原因分析
            </CardTitle>
            <CardDescription>
              了解触发因素，制定针对性策略
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[
                { reason: '压力过大', count: 8, percentage: 40, color: 'bg-red-500' },
                { reason: '情绪低落', count: 6, percentage: 30, color: 'bg-orange-500' },
                { reason: '无聊空虚', count: 4, percentage: 20, color: 'bg-yellow-500' },
                { reason: '社交媒体', count: 2, percentage: 10, color: 'bg-blue-500' }
              ].map((item, index) => (
                <div key={index} className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">{item.reason}</span>
                    <span className="text-sm text-gray-600">{item.count} 次</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className={`${item.color} h-2 rounded-full transition-all duration-300`}
                      style={{ width: `${item.percentage}%` }}
                    ></div>
                  </div>
                  <div className="text-right text-xs text-gray-500">
                    {item.percentage}%
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* 正气值变化曲线 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Zap className="mr-2 h-5 w-5" />
              正气值变化
            </CardTitle>
            <CardDescription>
              追踪您的精神状态提升过程
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-center space-x-4">
                <Button
                  variant={selectedMetric === 'streak' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setSelectedMetric('streak')}
                >
                  连续天数
                </Button>
                <Button
                  variant={selectedMetric === 'mood' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setSelectedMetric('mood')}
                >
                  情绪指数
                </Button>
                <Button
                  variant={selectedMetric === 'energy' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setSelectedMetric('energy')}
                >
                  精力水平
                </Button>
              </div>

              {/* 简化的图表展示 */}
              <div className="h-32 flex items-end justify-between space-x-1">
                {[65, 72, 68, 75, 82, 78, 85, 90, 88, 92, 95, 98].map((value, index) => (
                  <div key={index} className="flex-1 flex flex-col items-center">
                    <div
                      className="w-full bg-gradient-to-t from-blue-500 to-blue-300 rounded-t"
                      style={{ height: `${value}%` }}
                    ></div>
                    <div className="text-xs text-gray-500 mt-1">
                      {index + 1}
                    </div>
                  </div>
                ))}
              </div>

              <div className="text-center text-sm text-gray-600">
                最近12天的{selectedMetric === 'streak' ? '连续天数' :
                           selectedMetric === 'mood' ? '情绪指数' : '精力水平'}变化
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* AI生成的周度复盘报告 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Activity className="mr-2 h-5 w-5" />
            AI周度复盘报告
          </CardTitle>
          <CardDescription>
            基于您的数据生成的个性化分析报告
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="bg-gradient-to-r from-blue-50 to-purple-50 p-4 rounded-lg border border-blue-200">
              <h4 className="font-medium text-blue-900 mb-2">本周表现总结</h4>
              <p className="text-blue-800 text-sm">
                恭喜您！本周成功坚持了6天，相比上周有明显进步。您在周三和周五表现特别出色，
                情绪状态保持稳定，正气值持续上升。建议继续保持当前的作息规律和运动习惯。
              </p>
            </div>

            <div className="bg-gradient-to-r from-green-50 to-emerald-50 p-4 rounded-lg border border-green-200">
              <h4 className="font-medium text-green-900 mb-2">优势分析</h4>
              <ul className="text-green-800 text-sm space-y-1">
                <li>• 晨间冥想习惯已经养成，坚持率达到85%</li>
                <li>• 运动频率增加，有效缓解了压力</li>
                <li>• 社区互动积极，获得了良好的支持</li>
              </ul>
            </div>

            <div className="bg-gradient-to-r from-yellow-50 to-orange-50 p-4 rounded-lg border border-yellow-200">
              <h4 className="font-medium text-yellow-900 mb-2">改进建议</h4>
              <ul className="text-yellow-800 text-sm space-y-1">
                <li>• 周末时间管理需要加强，建议制定具体的活动计划</li>
                <li>• 深夜使用手机的频率较高，建议设置睡前提醒</li>
                <li>• 可以尝试增加户外活动，提升整体幸福感</li>
              </ul>
            </div>

            <div className="bg-gradient-to-r from-purple-50 to-pink-50 p-4 rounded-lg border border-purple-200">
              <h4 className="font-medium text-purple-900 mb-2">下周目标</h4>
              <p className="text-purple-800 text-sm">
                基于您的进步趋势，建议下周目标设定为：连续7天成功打卡，每天完成至少20分钟的户外活动，
                睡前1小时不使用电子设备。相信您一定能够达成这些目标！
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
