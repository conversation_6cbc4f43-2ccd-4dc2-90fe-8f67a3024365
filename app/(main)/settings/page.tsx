'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import {
  Settings,
  User,
  Bell,
  Shield,
  Palette,
  HelpCircle,
  LogOut,
  Save,
  Eye,
  EyeOff,
  Lock,
  Fingerprint,
  Smartphone,
  Database,
  Download,
  Trash2,
  AlertTriangle,
  Navigation,
  CheckCircle
} from 'lucide-react'

export default function SettingsPage() {
  const [activeSection, setActiveSection] = useState('account')
  const [showPassword, setShowPassword] = useState(false)
  const [formData, setFormData] = useState({
    username: '坚持的小明',
    email: '<EMAIL>',
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  })
  const [notifications, setNotifications] = useState({
    dailyReminder: true,
    weeklyReport: true,
    communityUpdates: false,
    achievementAlerts: true
  })
  const [privacy, setPrivacy] = useState({
    profileVisible: true,
    statsVisible: false,
    anonymousMode: true,
    biometricLock: false,
    hideFromRecents: false,
    dataEncryption: true,
    autoLogout: 15,
    incognitoMode: false
  })
  const [navigationSettings, setNavigationSettings] = useState({
    showDashboard: true,
    showPlan: true,
    showTracking: true,
    showAIAssistant: true,
    showTools: true,
    showEmergency: true,
    showLearn: true,
    showInspiration: true,
    showProgress: true,
    showHealth: true,
    showCommunity: true,
    showRecommendations: true,
    showDataDemo: false,
    showProfile: true,
    showSettings: true
  })

  const settingSections = [
    { id: 'account', name: '账户设置', icon: User },
    { id: 'navigation', name: '导航设置', icon: Settings },
    { id: 'notifications', name: '通知设置', icon: Bell },
    { id: 'privacy', name: '隐私安全', icon: Shield },
    { id: 'appearance', name: '外观设置', icon: Palette },
    { id: 'data', name: '数据管理', icon: Database },
    { id: 'help', name: '帮助支持', icon: HelpCircle }
  ]

  const navigationItems = [
    { key: 'showDashboard', name: '仪表盘', description: '显示主页仪表盘' },
    { key: 'showPlan', name: '我的计划', description: '个人戒色计划管理' },
    { key: 'showTracking', name: '每日追踪', description: '日常状态记录' },
    { key: 'showAIAssistant', name: 'AI智能助手', description: '人工智能对话支持' },
    { key: 'showTools', name: '防诱惑工具', description: '各种防诱惑工具' },
    { key: 'showEmergency', name: '紧急求助', description: '紧急情况支持' },
    { key: 'showLearn', name: '学习中心', description: '教育内容和资源' },
    { key: 'showInspiration', name: '正能量库', description: '励志内容和壁纸' },
    { key: 'showProgress', name: '进度统计', description: '数据分析和图表' },
    { key: 'showHealth', name: '健康监测', description: '身心健康监测' },
    { key: 'showCommunity', name: '社区', description: '用户交流社区' },
    { key: 'showRecommendations', name: 'AI推荐', description: '智能内容推荐' },
    { key: 'showDataDemo', name: '数据展示', description: '演示数据展示' },
    { key: 'showProfile', name: '个人中心', description: '个人信息管理' },
    { key: 'showSettings', name: '设置', description: '应用设置页面' }
  ]

  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)

  const handleInputChange = (field: string) => (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({
      ...prev,
      [field]: e.target.value
    }))
  }

  const handleNotificationChange = (field: string) => (checked: boolean) => {
    setNotifications(prev => ({
      ...prev,
      [field]: checked
    }))
  }

  const handlePrivacyChange = (field: string) => (checked: boolean) => {
    setPrivacy(prev => ({
      ...prev,
      [field]: checked
    }))
  }

  const handleSaveProfile = async () => {
    // 模拟保存个人信息
    console.log('保存个人信息:', formData)
  }

  const handleSaveNotifications = async () => {
    // 模拟保存通知设置
    console.log('保存通知设置:', notifications)
  }

  const handleSavePrivacy = async () => {
    // 模拟保存隐私设置
    console.log('保存隐私设置:', privacy)
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">设置</h1>
        <p className="text-gray-600">管理您的账户设置和偏好</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 设置导航 */}
        <Card className="lg:col-span-1">
          <CardHeader>
            <CardTitle className="text-lg">设置分类</CardTitle>
          </CardHeader>
          <CardContent>
            <nav className="space-y-2">
              {[
                { icon: User, label: '个人信息', id: 'profile' },
                { icon: Bell, label: '通知设置', id: 'notifications' },
                { icon: Shield, label: '隐私安全', id: 'privacy' },
                { icon: Database, label: '数据管理', id: 'data' },
                { icon: Palette, label: '外观主题', id: 'appearance' },
                { icon: HelpCircle, label: '帮助支持', id: 'help' }
              ].map((item) => {
                const Icon = item.icon
                return (
                  <a
                    key={item.id}
                    href={`#${item.id}`}
                    className="flex items-center px-3 py-2 text-sm font-medium text-gray-600 rounded-md hover:bg-gray-50 hover:text-gray-900 transition-colors"
                  >
                    <Icon className="mr-3 h-4 w-4" />
                    {item.label}
                  </a>
                )
              })}
            </nav>
          </CardContent>
        </Card>

        {/* 设置内容 */}
        <div className="lg:col-span-2 space-y-6">
          {/* 个人信息 */}
          <Card id="profile">
            <CardHeader>
              <CardTitle className="flex items-center">
                <User className="mr-2 h-5 w-5" />
                个人信息
              </CardTitle>
              <CardDescription>
                更新您的个人资料和账户信息
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">用户名</label>
                  <Input
                    value={formData.username}
                    onChange={handleInputChange('username')}
                    placeholder="请输入用户名"
                  />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">邮箱</label>
                  <Input
                    type="email"
                    value={formData.email}
                    onChange={handleInputChange('email')}
                    placeholder="请输入邮箱"
                  />
                </div>
              </div>
              
              <div className="pt-4 border-t">
                <h4 className="text-sm font-medium mb-4">修改密码</h4>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">当前密码</label>
                    <div className="relative">
                      <Input
                        type={showPassword ? 'text' : 'password'}
                        value={formData.currentPassword}
                        onChange={handleInputChange('currentPassword')}
                        placeholder="请输入当前密码"
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="icon"
                        className="absolute right-0 top-0 h-full px-3"
                        onClick={() => setShowPassword(!showPassword)}
                      >
                        {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      </Button>
                    </div>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <label className="text-sm font-medium">新密码</label>
                      <Input
                        type="password"
                        value={formData.newPassword}
                        onChange={handleInputChange('newPassword')}
                        placeholder="请输入新密码"
                      />
                    </div>
                    <div className="space-y-2">
                      <label className="text-sm font-medium">确认新密码</label>
                      <Input
                        type="password"
                        value={formData.confirmPassword}
                        onChange={handleInputChange('confirmPassword')}
                        placeholder="请再次输入新密码"
                      />
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="flex justify-end pt-4">
                <Button onClick={handleSaveProfile}>
                  <Save className="mr-2 h-4 w-4" />
                  保存更改
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* 通知设置 */}
          <Card id="notifications">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Bell className="mr-2 h-5 w-5" />
                通知设置
              </CardTitle>
              <CardDescription>
                管理您接收的通知类型
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {[
                { key: 'dailyReminder', label: '每日提醒', description: '每天定时提醒您打卡' },
                { key: 'weeklyReport', label: '周度报告', description: '每周发送进度总结报告' },
                { key: 'communityUpdates', label: '社区动态', description: '接收社区新帖子和回复通知' },
                { key: 'achievementAlerts', label: '成就提醒', description: '达成新成就时通知您' }
              ].map((item) => (
                <div key={item.key} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex-1">
                    <h4 className="font-medium">{item.label}</h4>
                    <p className="text-sm text-gray-600 mt-1">{item.description}</p>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      className="sr-only peer"
                      checked={notifications[item.key as keyof typeof notifications]}
                      onChange={(e) => handleNotificationChange(item.key)(e.target.checked)}
                    />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                  </label>
                </div>
              ))}
              
              <div className="flex justify-end pt-4">
                <Button onClick={handleSaveNotifications}>
                  <Save className="mr-2 h-4 w-4" />
                  保存设置
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* 隐私安全 */}
          <Card id="privacy">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Shield className="mr-2 h-5 w-5" />
                隐私安全
              </CardTitle>
              <CardDescription>
                控制您的隐私和数据安全设置
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* 基础隐私设置 */}
              <div className="space-y-4">
                <h4 className="font-medium text-gray-900 border-b pb-2">基础隐私</h4>
                {[
                  { key: 'profileVisible', label: '公开个人资料', description: '允许其他用户查看您的基本信息', icon: User },
                  { key: 'statsVisible', label: '公开统计数据', description: '在社区中显示您的进度统计', icon: Database },
                  { key: 'anonymousMode', label: '匿名模式', description: '在社区中使用匿名身份', icon: Eye }
                ].map((item) => (
                  <div key={item.key} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-3">
                      <item.icon className="h-5 w-5 text-blue-500" />
                      <div className="flex-1">
                        <h4 className="font-medium">{item.label}</h4>
                        <p className="text-sm text-gray-600 mt-1">{item.description}</p>
                      </div>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        className="sr-only peer"
                        checked={privacy[item.key as keyof typeof privacy]}
                        onChange={(e) => handlePrivacyChange(item.key)(e.target.checked)}
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                    </label>
                  </div>
                ))}
              </div>

              {/* 高级安全设置 */}
              <div className="space-y-4">
                <h4 className="font-medium text-gray-900 border-b pb-2">高级安全</h4>
                {[
                  { key: 'biometricLock', label: '生物识别锁定', description: '使用指纹或面部识别解锁应用', icon: Fingerprint },
                  { key: 'hideFromRecents', label: '从最近使用中隐藏', description: '在系统最近使用的应用列表中隐藏', icon: Smartphone },
                  { key: 'dataEncryption', label: '数据加密', description: '本地数据使用AES-256加密存储', icon: Lock },
                  { key: 'incognitoMode', label: '隐身模式', description: '不记录使用痕迹，退出时清除数据', icon: EyeOff }
                ].map((item) => (
                  <div key={item.key} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-3">
                      <item.icon className="h-5 w-5 text-green-500" />
                      <div className="flex-1">
                        <h4 className="font-medium">{item.label}</h4>
                        <p className="text-sm text-gray-600 mt-1">{item.description}</p>
                      </div>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        className="sr-only peer"
                        checked={privacy[item.key as keyof typeof privacy]}
                        onChange={(e) => handlePrivacyChange(item.key)(e.target.checked)}
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                    </label>
                  </div>
                ))}

                {/* 自动锁定时间设置 */}
                <div className="p-4 border rounded-lg">
                  <div className="flex items-center space-x-3 mb-3">
                    <AlertTriangle className="h-5 w-5 text-orange-500" />
                    <div>
                      <h4 className="font-medium">自动锁定时间</h4>
                      <p className="text-sm text-gray-600">应用在后台运行多长时间后自动锁定</p>
                    </div>
                  </div>
                  <select
                    value={privacy.autoLogout}
                    onChange={(e) => handlePrivacyChange('autoLogout')(parseInt(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                  >
                    <option value={5}>5分钟</option>
                    <option value={15}>15分钟</option>
                    <option value={30}>30分钟</option>
                    <option value={60}>1小时</option>
                    <option value={0}>从不</option>
                  </select>
                </div>
              </div>
              
              <div className="flex justify-end pt-4">
                <Button onClick={handleSavePrivacy}>
                  <Save className="mr-2 h-4 w-4" />
                  保存设置
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* 数据管理 */}
          <Card id="data">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Database className="mr-2 h-5 w-5" />
                数据管理
              </CardTitle>
              <CardDescription>
                管理您的数据备份、导出和删除
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* 数据导出 */}
              <div className="space-y-4">
                <h4 className="font-medium text-gray-900 border-b pb-2">数据导出</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="p-4 border rounded-lg">
                    <div className="flex items-center space-x-3 mb-3">
                      <Download className="h-5 w-5 text-blue-500" />
                      <div>
                        <h4 className="font-medium">导出个人数据</h4>
                        <p className="text-sm text-gray-600">下载您的所有个人数据副本</p>
                      </div>
                    </div>
                    <Button variant="outline" className="w-full">
                      <Download className="mr-2 h-4 w-4" />
                      导出数据
                    </Button>
                  </div>

                  <div className="p-4 border rounded-lg">
                    <div className="flex items-center space-x-3 mb-3">
                      <Settings className="h-5 w-5 text-green-500" />
                      <div>
                        <h4 className="font-medium">导出设置</h4>
                        <p className="text-sm text-gray-600">备份您的应用设置和偏好</p>
                      </div>
                    </div>
                    <Button variant="outline" className="w-full">
                      <Download className="mr-2 h-4 w-4" />
                      导出设置
                    </Button>
                  </div>
                </div>
              </div>

              {/* 数据清理 */}
              <div className="space-y-4">
                <h4 className="font-medium text-gray-900 border-b pb-2">数据清理</h4>
                <div className="space-y-3">
                  <div className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-3">
                      <Trash2 className="h-5 w-5 text-orange-500" />
                      <div>
                        <h4 className="font-medium">清除缓存</h4>
                        <p className="text-sm text-gray-600">清除应用缓存数据，释放存储空间</p>
                      </div>
                    </div>
                    <Button variant="outline" size="sm">
                      清除缓存
                    </Button>
                  </div>

                  <div className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-3">
                      <Database className="h-5 w-5 text-purple-500" />
                      <div>
                        <h4 className="font-medium">重置统计数据</h4>
                        <p className="text-sm text-gray-600">清除所有进度统计，重新开始</p>
                      </div>
                    </div>
                    <Button variant="outline" size="sm">
                      重置数据
                    </Button>
                  </div>
                </div>
              </div>

              {/* 数据保留政策 */}
              <div className="space-y-4">
                <h4 className="font-medium text-gray-900 border-b pb-2">数据保留</h4>
                <div className="p-4 border rounded-lg bg-blue-50">
                  <div className="flex items-start space-x-3">
                    <Shield className="h-5 w-5 text-blue-500 mt-0.5" />
                    <div>
                      <h4 className="font-medium text-blue-900">数据保护承诺</h4>
                      <p className="text-sm text-blue-800 mt-1">
                        我们承诺保护您的隐私数据，所有敏感信息都经过加密处理。
                        您的数据仅用于提供服务，不会与第三方共享。
                      </p>
                      <ul className="text-sm text-blue-700 mt-2 space-y-1">
                        <li>• 本地数据使用AES-256加密</li>
                        <li>• 云端备份采用端到端加密</li>
                        <li>• 匿名数据用于改进服务</li>
                        <li>• 遵循GDPR和相关隐私法规</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 危险操作 */}
          <Card className="border-red-200">
            <CardHeader>
              <CardTitle className="text-red-600 flex items-center">
                <AlertTriangle className="mr-2 h-5 w-5" />
                危险操作
              </CardTitle>
              <CardDescription>
                这些操作不可逆，请谨慎操作
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between p-4 border border-red-200 rounded-lg bg-red-50">
                <div className="flex items-center space-x-3">
                  <Trash2 className="h-5 w-5 text-red-500" />
                  <div>
                    <h4 className="font-medium text-red-800">删除所有数据</h4>
                    <p className="text-sm text-red-600 mt-1">
                      永久删除您的所有数据，包括进度记录和设置
                    </p>
                  </div>
                </div>
                <Button
                  variant="destructive"
                  onClick={() => setShowDeleteConfirm(true)}
                >
                  删除数据
                </Button>
              </div>

              <div className="flex items-center justify-between p-4 border border-red-200 rounded-lg bg-red-50">
                <div className="flex items-center space-x-3">
                  <LogOut className="h-5 w-5 text-red-500" />
                  <div>
                    <h4 className="font-medium text-red-800">删除账户</h4>
                    <p className="text-sm text-red-600 mt-1">
                      永久删除您的账户和所有数据，此操作不可恢复
                    </p>
                  </div>
                </div>
                <Button variant="destructive">
                  删除账户
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* 删除确认对话框 */}
          {showDeleteConfirm && (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
              <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
                <div className="flex items-center space-x-3 mb-4">
                  <AlertTriangle className="h-6 w-6 text-red-500" />
                  <h3 className="text-lg font-medium text-gray-900">确认删除</h3>
                </div>
                <p className="text-gray-600 mb-6">
                  您确定要删除所有数据吗？此操作将永久删除您的进度记录、设置和所有相关数据，且无法恢复。
                </p>
                <div className="flex space-x-3">
                  <Button
                    variant="outline"
                    className="flex-1"
                    onClick={() => setShowDeleteConfirm(false)}
                  >
                    取消
                  </Button>
                  <Button
                    variant="destructive"
                    className="flex-1"
                    onClick={() => {
                      // 处理删除逻辑
                      setShowDeleteConfirm(false)
                      alert('数据已删除')
                    }}
                  >
                    确认删除
                  </Button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
