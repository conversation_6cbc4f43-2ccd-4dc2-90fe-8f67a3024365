'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  Sparkles,
  Heart,
  Star,
  Quote,
  Shuffle,
  BookOpen,
  Lightbulb,
  Target,
  Zap,
  Sun,
  Mountain,
  Flame,
  RefreshCw,
  Share,
  Copy,
  Download,
  Plus
} from 'lucide-react'
import { mockApiResponse } from '@/lib/mock-data'

// 生成正能量内容数据
function generateInspirationData() {
  const dailySlogans = [
    {
      id: 'slogan-1',
      text: '每一次选择，都是重新定义自己的机会',
      category: '自我成长',
      author: '戒色导师',
      likes: 1247,
      shares: 89,
      color: 'from-blue-500 to-purple-600'
    },
    {
      id: 'slogan-2', 
      text: '真正的力量来自内心的平静与坚定',
      category: '内心力量',
      author: '心理学专家',
      likes: 956,
      shares: 67,
      color: 'from-green-500 to-teal-600'
    },
    {
      id: 'slogan-3',
      text: '今天的坚持，是明天更好自己的基石',
      category: '坚持不懈',
      author: '成功学导师',
      likes: 1834,
      shares: 123,
      color: 'from-orange-500 to-red-600'
    },
    {
      id: 'slogan-4',
      text: '改变从接受现在的自己开始，成长从行动开始',
      category: '积极改变',
      author: '生活哲学家',
      likes: 2156,
      shares: 178,
      color: 'from-purple-500 to-pink-600'
    }
  ]

  const inspirationalQuotes = [
    {
      id: 'quote-1',
      text: '人生最大的敌人不是别人，而是昨天的自己。每一天都是超越自己的机会。',
      author: '李开复',
      category: '自我超越',
      tags: ['成长', '坚持', '改变'],
      source: '《做最好的自己》',
      difficulty: 'beginner',
      effectiveness: 95
    },
    {
      id: 'quote-2',
      text: '真正的自由不是想做什么就做什么，而是不想做什么就不做什么。',
      author: '康德',
      category: '自由意志',
      tags: ['自控', '自由', '意志力'],
      source: '哲学经典',
      difficulty: 'intermediate',
      effectiveness: 88
    },
    {
      id: 'quote-3',
      text: '种一棵树最好的时间是十年前，其次是现在。改变自己也是如此。',
      author: '中国谚语',
      category: '行动力',
      tags: ['行动', '改变', '当下'],
      source: '民间智慧',
      difficulty: 'beginner',
      effectiveness: 92
    },
    {
      id: 'quote-4',
      text: '痛苦是成长的催化剂，困难是强者的垫脚石。',
      author: '尼采',
      category: '逆境成长',
      tags: ['困难', '成长', '坚强'],
      source: '《查拉图斯特拉如是说》',
      difficulty: 'advanced',
      effectiveness: 85
    },
    {
      id: 'quote-5',
      text: '习惯决定性格，性格决定命运。好习惯是成功的开始。',
      author: '亚里士多德',
      category: '习惯养成',
      tags: ['习惯', '性格', '命运'],
      source: '《尼各马可伦理学》',
      difficulty: 'intermediate',
      effectiveness: 90
    }
  ]

  const inspirationalArticles = [
    {
      id: 'article-1',
      title: '戒色的科学原理：大脑神经可塑性研究',
      summary: '深入了解戒色过程中大脑的变化机制，基于最新神经科学研究成果',
      category: '科学知识',
      reading_time: 12,
      difficulty: 'intermediate',
      author: '神经科学研究院',
      tags: ['神经科学', '大脑健康', '科学研究'],
      views: 15420,
      likes: 892,
      published_date: '2024-01-15'
    },
    {
      id: 'article-2',
      title: '正念冥想在戒色中的应用',
      summary: '如何通过正念冥想技巧来增强自控力，管理冲动和欲望',
      category: '实践方法',
      reading_time: 8,
      difficulty: 'beginner',
      author: '正念导师',
      tags: ['正念', '冥想', '自控力'],
      views: 12350,
      likes: 756,
      published_date: '2024-01-10'
    },
    {
      id: 'article-3',
      title: '从心理学角度理解成瘾机制',
      summary: '深度解析成瘾的心理机制，为戒色提供科学的理论基础',
      category: '心理学',
      reading_time: 15,
      difficulty: 'advanced',
      author: '心理学博士',
      tags: ['心理学', '成瘾机制', '理论基础'],
      views: 9876,
      likes: 543,
      published_date: '2024-01-08'
    },
    {
      id: 'article-4',
      title: '建立健康生活习惯的21天法则',
      summary: '如何在21天内建立新的健康习惯，替代不良行为模式',
      category: '习惯养成',
      reading_time: 6,
      difficulty: 'beginner',
      author: '习惯专家',
      tags: ['习惯', '21天', '生活方式'],
      views: 18750,
      likes: 1234,
      published_date: '2024-01-12'
    }
  ]

  const inspirationalActivities = [
    {
      id: 'activity-1',
      title: '30天冷水澡挑战',
      description: '通过30天的冷水澡练习，增强意志力和身体抗压能力',
      category: '身体锻炼',
      duration: '30天',
      difficulty: 'intermediate',
      participants: 2456,
      success_rate: 78,
      benefits: ['增强意志力', '提高免疫力', '改善循环'],
      requirements: ['每天冷水澡5-10分钟', '记录感受', '坚持打卡']
    },
    {
      id: 'activity-2',
      title: '每日感恩日记',
      description: '每天记录3件感恩的事情，培养积极心态',
      category: '心理建设',
      duration: '21天',
      difficulty: 'beginner',
      participants: 5678,
      success_rate: 85,
      benefits: ['提升幸福感', '改善心态', '增强正能量'],
      requirements: ['每天写3件感恩的事', '坚持21天', '分享感悟']
    },
    {
      id: 'activity-3',
      title: '数字断食挑战',
      description: '限制社交媒体和娱乐应用使用时间，重新掌控注意力',
      category: '数字健康',
      duration: '7天',
      difficulty: 'hard',
      participants: 1234,
      success_rate: 65,
      benefits: ['提高专注力', '减少诱惑', '改善睡眠'],
      requirements: ['限制手机使用', '删除诱惑应用', '寻找替代活动']
    },
    {
      id: 'activity-4',
      title: '晨间例行公事建立',
      description: '建立健康的晨间习惯，为一天注入正能量',
      category: '生活习惯',
      duration: '14天',
      difficulty: 'beginner',
      participants: 3456,
      success_rate: 82,
      benefits: ['提升精力', '增强自律', '改善心情'],
      requirements: ['早起30分钟', '固定晨间流程', '坚持执行']
    }
  ]

  const recommendedBooks = [
    {
      id: 'book-1',
      title: '原子习惯',
      author: '詹姆斯·克利尔',
      category: '习惯养成',
      rating: 4.8,
      pages: 320,
      reading_time: '6小时',
      summary: '微小的改变，如何带来巨大的差异。系统性地介绍了如何建立好习惯、改掉坏习惯',
      key_points: ['1%的改进', '习惯回路', '环境设计', '身份认同'],
      relevance: 95,
      difficulty: 'beginner'
    },
    {
      id: 'book-2',
      title: '意志力',
      author: '罗伊·鲍迈斯特',
      category: '心理学',
      rating: 4.6,
      pages: 280,
      reading_time: '5小时',
      summary: '关于意志力的权威研究，揭示了自控力的科学原理和提升方法',
      key_points: ['意志力有限', '葡萄糖理论', '自控训练', '环境影响'],
      relevance: 92,
      difficulty: 'intermediate'
    },
    {
      id: 'book-3',
      title: '正念的奇迹',
      author: '一行禅师',
      category: '正念冥想',
      rating: 4.7,
      pages: 200,
      reading_time: '4小时',
      summary: '正念冥想的入门经典，教导如何在日常生活中保持觉察和平静',
      key_points: ['当下觉察', '呼吸冥想', '行走冥想', '日常正念'],
      relevance: 88,
      difficulty: 'beginner'
    },
    {
      id: 'book-4',
      title: '心流',
      author: '米哈里·契克森米哈赖',
      category: '积极心理学',
      rating: 4.5,
      pages: 350,
      reading_time: '7小时',
      summary: '探索最优体验的心理学，如何在活动中找到深度满足感',
      key_points: ['心流状态', '挑战与技能', '明确目标', '即时反馈'],
      relevance: 85,
      difficulty: 'intermediate'
    },
    {
      id: 'book-5',
      title: '不抱怨的世界',
      author: '威尔·鲍温',
      category: '积极思维',
      rating: 4.4,
      pages: 240,
      reading_time: '4小时',
      summary: '通过21天不抱怨挑战，改变思维模式，创造积极的人生',
      key_points: ['抱怨的危害', '意识觉察', '积极表达', '环境影响'],
      relevance: 80,
      difficulty: 'beginner'
    }
  ]

  const dailyAffirmations = [
    '我有能力控制自己的行为和想法',
    '每一天我都在变得更强大',
    '我值得拥有健康和快乐的生活',
    '困难只是暂时的，我的意志是永恒的',
    '我选择用积极的方式面对挑战',
    '今天的我比昨天更好',
    '我有勇气改变可以改变的事情',
    '我的未来掌握在自己手中',
    '每一次坚持都让我更接近目标',
    '我相信自己有无限的潜能'
  ]

  return {
    dailySlogans,
    inspirationalQuotes,
    inspirationalArticles,
    inspirationalActivities,
    recommendedBooks,
    dailyAffirmations
  }
}

export default function InspirationPage() {
  const [inspirationData, setInspirationData] = useState<any>(null)
  const [currentSlogan, setCurrentSlogan] = useState<any>(null)
  const [currentQuote, setCurrentQuote] = useState<any>(null)
  const [currentAffirmation, setCurrentAffirmation] = useState<string>('')
  const [loading, setLoading] = useState(false)
  const [activeTab, setActiveTab] = useState<'slogans' | 'articles' | 'activities' | 'books'>('slogans')

  const loadInspirationData = async () => {
    setLoading(true)
    try {
      const response = await mockApiResponse(generateInspirationData())
      const data = response.data
      setInspirationData(data)
      
      // 设置今日内容
      setCurrentSlogan(data.dailySlogans[0])
      setCurrentQuote(data.inspirationalQuotes[0])
      setCurrentAffirmation(data.dailyAffirmations[0])
    } catch (error) {
      console.error('加载正能量数据失败:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadInspirationData()
  }, [])

  const shuffleSlogan = () => {
    if (inspirationData?.dailySlogans) {
      const randomSlogan = inspirationData.dailySlogans[
        Math.floor(Math.random() * inspirationData.dailySlogans.length)
      ]
      setCurrentSlogan(randomSlogan)
    }
  }

  const shuffleQuote = () => {
    if (inspirationData?.inspirationalQuotes) {
      const randomQuote = inspirationData.inspirationalQuotes[
        Math.floor(Math.random() * inspirationData.inspirationalQuotes.length)
      ]
      setCurrentQuote(randomQuote)
    }
  }

  const shuffleAffirmation = () => {
    if (inspirationData?.dailyAffirmations) {
      const randomAffirmation = inspirationData.dailyAffirmations[
        Math.floor(Math.random() * inspirationData.dailyAffirmations.length)
      ]
      setCurrentAffirmation(randomAffirmation)
    }
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    // 这里可以添加toast提示
  }

  if (!inspirationData) {
    return <div>加载中...</div>
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 flex items-center">
            <Sparkles className="mr-2 h-6 w-6 text-yellow-500" />
            正能量库
          </h1>
          <p className="text-gray-600">汲取智慧力量，点燃内心火焰，坚定前行步伐</p>
        </div>
        <Button 
          onClick={loadInspirationData} 
          disabled={loading}
          variant="gradient"
        >
          <RefreshCw className={`mr-2 h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
          {loading ? '更新中...' : '刷新内容'}
        </Button>
      </div>

      {/* 今日Slogan */}
      {currentSlogan && (
        <Card className="relative overflow-hidden">
          <div className={`absolute inset-0 bg-gradient-to-r ${currentSlogan.color} opacity-10`}></div>
          <CardContent className="relative p-8 text-center">
            <div className="flex items-center justify-center mb-4">
              <Sun className="h-8 w-8 text-yellow-500 mr-2" />
              <h2 className="text-xl font-bold text-gray-900">今日金句</h2>
            </div>
            <blockquote className="text-2xl font-medium text-gray-900 mb-4 leading-relaxed">
              "{currentSlogan.text}"
            </blockquote>
            <div className="flex items-center justify-center gap-4 mb-4">
              <Badge variant="secondary">{currentSlogan.category}</Badge>
              <span className="text-sm text-gray-600">— {currentSlogan.author}</span>
            </div>
            <div className="flex items-center justify-center gap-2 text-sm text-gray-500 mb-4">
              <Heart className="h-4 w-4" />
              <span>{currentSlogan.likes}</span>
              <Share className="h-4 w-4 ml-2" />
              <span>{currentSlogan.shares}</span>
            </div>
            <div className="flex justify-center gap-2">
              <Button size="sm" variant="outline" onClick={shuffleSlogan}>
                <Shuffle className="mr-2 h-4 w-4" />
                换一句
              </Button>
              <Button size="sm" variant="outline" onClick={() => copyToClipboard(currentSlogan.text)}>
                <Copy className="mr-2 h-4 w-4" />
                复制
              </Button>
              <Button size="sm" variant="outline">
                <Share className="mr-2 h-4 w-4" />
                分享
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 分类导航 */}
      <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
        <button
          onClick={() => setActiveTab('slogans')}
          className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'slogans'
              ? 'bg-white text-blue-600 shadow-sm'
              : 'text-gray-600 hover:text-gray-900'
          }`}
        >
          金句格言
        </button>
        <button
          onClick={() => setActiveTab('articles')}
          className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'articles'
              ? 'bg-white text-blue-600 shadow-sm'
              : 'text-gray-600 hover:text-gray-900'
          }`}
        >
          精选文章
        </button>
        <button
          onClick={() => setActiveTab('activities')}
          className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'activities'
              ? 'bg-white text-blue-600 shadow-sm'
              : 'text-gray-600 hover:text-gray-900'
          }`}
        >
          挑战活动
        </button>
        <button
          onClick={() => setActiveTab('books')}
          className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'books'
              ? 'bg-white text-blue-600 shadow-sm'
              : 'text-gray-600 hover:text-gray-900'
          }`}
        >
          推荐书籍
        </button>
      </div>

      {/* 内容展示区域 */}
      {activeTab === 'slogans' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 每日肯定语 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Zap className="mr-2 h-5 w-5 text-orange-500" />
              每日肯定语
            </CardTitle>
            <CardDescription>
              积极的自我暗示，建立强大的内心力量
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-center p-6 bg-gradient-to-r from-orange-50 to-yellow-50 rounded-lg">
              <Quote className="h-8 w-8 text-orange-500 mx-auto mb-4" />
              <p className="text-lg font-medium text-gray-900 mb-4">
                {currentAffirmation}
              </p>
              <Button size="sm" onClick={shuffleAffirmation} variant="outline">
                <Shuffle className="mr-2 h-4 w-4" />
                换一句
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* 智慧名言 */}
        {currentQuote && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <BookOpen className="mr-2 h-5 w-5 text-blue-500" />
                智慧名言
              </CardTitle>
              <CardDescription>
                古今中外的智慧结晶，指引人生方向
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <blockquote className="text-base text-gray-700 leading-relaxed border-l-4 border-blue-500 pl-4">
                  {currentQuote.text}
                </blockquote>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium text-gray-900">— {currentQuote.author}</p>
                    <p className="text-sm text-gray-500">{currentQuote.source}</p>
                  </div>
                  <div className="flex gap-2">
                    <Badge variant="outline">{currentQuote.category}</Badge>
                    <Badge variant="secondary">效果 {currentQuote.effectiveness}%</Badge>
                  </div>
                </div>
                <div className="flex flex-wrap gap-1">
                  {currentQuote.tags.map((tag: string, index: number) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      #{tag}
                    </Badge>
                  ))}
                </div>
                <div className="flex justify-center">
                  <Button size="sm" onClick={shuffleQuote} variant="outline">
                    <Shuffle className="mr-2 h-4 w-4" />
                    换一句
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* 精选文章 */}
      {activeTab === 'articles' && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <BookOpen className="mr-2 h-5 w-5 text-blue-500" />
              精选文章
            </CardTitle>
            <CardDescription>
              深度好文，提供科学指导和实用方法
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {inspirationData?.inspirationalArticles?.map((article: any) => (
                <div key={article.id} className="p-6 border rounded-lg hover:shadow-md transition-shadow cursor-pointer">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <Badge variant="outline">{article.category}</Badge>
                        <Badge variant="secondary" className={
                          article.difficulty === 'beginner' ? 'bg-green-100 text-green-800' :
                          article.difficulty === 'intermediate' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-red-100 text-red-800'
                        }>
                          {article.difficulty === 'beginner' ? '入门' :
                           article.difficulty === 'intermediate' ? '进阶' : '高级'}
                        </Badge>
                      </div>
                      <h3 className="text-lg font-semibold text-gray-900 mb-2">{article.title}</h3>
                      <p className="text-gray-600 mb-3">{article.summary}</p>
                      <div className="flex items-center gap-4 text-sm text-gray-500 mb-3">
                        <span>📖 {article.reading_time} 分钟</span>
                        <span>👁️ {article.views.toLocaleString()}</span>
                        <span>❤️ {article.likes}</span>
                        <span>✍️ {article.author}</span>
                      </div>
                      <div className="flex flex-wrap gap-1">
                        {article.tags.map((tag: string, index: number) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            #{tag}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>
                  <Button className="w-full">
                    <BookOpen className="mr-2 h-4 w-4" />
                    阅读全文
                  </Button>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* 挑战活动 */}
      {activeTab === 'activities' && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Target className="mr-2 h-5 w-5 text-orange-500" />
              挑战活动
            </CardTitle>
            <CardDescription>
              参与挑战，在行动中成长和蜕变
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {inspirationData?.inspirationalActivities?.map((activity: any) => (
                <div key={activity.id} className="p-6 border rounded-lg hover:shadow-md transition-shadow">
                  <div className="flex items-center gap-2 mb-3">
                    <Badge variant="outline">{activity.category}</Badge>
                    <Badge variant="secondary" className={
                      activity.difficulty === 'beginner' ? 'bg-green-100 text-green-800' :
                      activity.difficulty === 'intermediate' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-red-100 text-red-800'
                    }>
                      {activity.difficulty === 'beginner' ? '简单' :
                       activity.difficulty === 'intermediate' ? '中等' : '困难'}
                    </Badge>
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">{activity.title}</h3>
                  <p className="text-gray-600 mb-4">{activity.description}</p>

                  <div className="grid grid-cols-2 gap-4 mb-4">
                    <div className="text-center p-3 bg-gray-50 rounded-lg">
                      <div className="text-xl font-bold text-orange-600">{activity.participants}</div>
                      <div className="text-xs text-gray-600">参与人数</div>
                    </div>
                    <div className="text-center p-3 bg-gray-50 rounded-lg">
                      <div className="text-xl font-bold text-green-600">{activity.success_rate}%</div>
                      <div className="text-xs text-gray-600">成功率</div>
                    </div>
                  </div>

                  <div className="mb-4">
                    <h4 className="font-medium text-gray-900 mb-2">预期收益：</h4>
                    <div className="flex flex-wrap gap-1">
                      {activity.benefits.map((benefit: string, index: number) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          ✓ {benefit}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  <div className="mb-4">
                    <h4 className="font-medium text-gray-900 mb-2">参与要求：</h4>
                    <ul className="text-sm text-gray-600 space-y-1">
                      {activity.requirements.map((req: string, index: number) => (
                        <li key={index} className="flex items-center gap-2">
                          <div className="w-1.5 h-1.5 bg-orange-500 rounded-full"></div>
                          {req}
                        </li>
                      ))}
                    </ul>
                  </div>

                  <Button className="w-full">
                    <Plus className="mr-2 h-4 w-4" />
                    参加挑战 ({activity.duration})
                  </Button>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* 推荐书籍 */}
      {activeTab === 'books' && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <BookOpen className="mr-2 h-5 w-5 text-purple-500" />
              推荐书籍
            </CardTitle>
            <CardDescription>
              精选好书，汲取智慧，指导人生
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {inspirationData?.recommendedBooks?.map((book: any) => (
                <div key={book.id} className="p-6 border rounded-lg hover:shadow-md transition-shadow">
                  <div className="flex items-start gap-4">
                    <div className="w-20 h-28 bg-gradient-to-br from-purple-100 to-blue-100 rounded-lg flex items-center justify-center">
                      <BookOpen className="h-8 w-8 text-purple-600" />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <Badge variant="outline">{book.category}</Badge>
                        <Badge variant="secondary" className={
                          book.difficulty === 'beginner' ? 'bg-green-100 text-green-800' :
                          'bg-yellow-100 text-yellow-800'
                        }>
                          {book.difficulty === 'beginner' ? '易读' : '进阶'}
                        </Badge>
                        <div className="flex items-center gap-1">
                          <Star className="h-4 w-4 text-yellow-500" />
                          <span className="text-sm font-medium">{book.rating}</span>
                        </div>
                      </div>
                      <h3 className="text-xl font-bold text-gray-900 mb-1">{book.title}</h3>
                      <p className="text-gray-600 mb-3">作者：{book.author}</p>
                      <p className="text-gray-700 mb-4">{book.summary}</p>

                      <div className="grid grid-cols-3 gap-4 mb-4 text-sm">
                        <div className="text-center p-2 bg-gray-50 rounded">
                          <div className="font-medium">{book.pages}</div>
                          <div className="text-gray-500">页数</div>
                        </div>
                        <div className="text-center p-2 bg-gray-50 rounded">
                          <div className="font-medium">{book.reading_time}</div>
                          <div className="text-gray-500">阅读时长</div>
                        </div>
                        <div className="text-center p-2 bg-gray-50 rounded">
                          <div className="font-medium">{book.relevance}%</div>
                          <div className="text-gray-500">相关度</div>
                        </div>
                      </div>

                      <div className="mb-4">
                        <h4 className="font-medium text-gray-900 mb-2">核心要点：</h4>
                        <div className="flex flex-wrap gap-2">
                          {book.key_points.map((point: string, index: number) => (
                            <Badge key={index} variant="outline" className="text-xs">
                              • {point}
                            </Badge>
                          ))}
                        </div>
                      </div>

                      <div className="flex gap-2">
                        <Button className="flex-1">
                          <BookOpen className="mr-2 h-4 w-4" />
                          查看详情
                        </Button>
                        <Button variant="outline">
                          <Download className="mr-2 h-4 w-4" />
                          收藏
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* 快速行动 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Target className="mr-2 h-5 w-5 text-purple-500" />
            立即行动
          </CardTitle>
          <CardDescription>
            将正能量转化为实际行动
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Button className="h-20 flex-col" variant="outline">
              <Lightbulb className="h-6 w-6 mb-2" />
              <span>制定目标</span>
            </Button>
            <Button className="h-20 flex-col" variant="outline">
              <Heart className="h-6 w-6 mb-2" />
              <span>感恩练习</span>
            </Button>
            <Button className="h-20 flex-col" variant="outline">
              <Mountain className="h-6 w-6 mb-2" />
              <span>冥想放松</span>
            </Button>
            <Button className="h-20 flex-col" variant="outline">
              <Share className="h-6 w-6 mb-2" />
              <span>分享感悟</span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
