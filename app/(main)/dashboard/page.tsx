'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { 
  Calendar,
  TrendingUp,
  Target,
  Zap,
  CheckCircle,
  Clock,
  Award,
  Flame
} from 'lucide-react'
import { 
  generateMockCheckInRecords, 
  generateMockUserStats, 
  mockDailyTasks,
  mockApiResponse 
} from '@/lib/mock-data'
import { calculateStreak, formatDate, getTodayString, isToday } from '@/lib/utils'
import type { CheckInRecord, UserStats, DailyTask } from '@/types'

export default function DashboardPage() {
  const [stats, setStats] = useState<UserStats | null>(null)
  const [todayTasks, setTodayTasks] = useState<DailyTask[]>([])
  const [checkInRecords, setCheckInRecords] = useState<CheckInRecord[]>([])
  const [todayCheckedIn, setTodayCheckedIn] = useState(false)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadDashboardData()
  }, [])

  const loadDashboardData = async () => {
    try {
      // 模拟加载数据
      const [statsResponse, tasksResponse, recordsResponse] = await Promise.all([
        mockApiResponse(generateMockUserStats()),
        mockApiResponse(mockDailyTasks),
        mockApiResponse(generateMockCheckInRecords(30))
      ])

      setStats(statsResponse.data)
      setTodayTasks(tasksResponse.data)
      setCheckInRecords(recordsResponse.data)
      
      // 检查今天是否已打卡
      const todayRecord = recordsResponse.data.find(record => 
        isToday(record.check_in_date)
      )
      setTodayCheckedIn(!!todayRecord)
      
    } catch (error) {
      console.error('加载数据失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleCheckIn = async () => {
    if (todayCheckedIn) return

    try {
      // 模拟打卡API调用
      await mockApiResponse({
        id: `checkin-${Date.now()}`,
        user_id: 'mock-user-123',
        check_in_date: getTodayString(),
        status: 'success',
        mood_level: 4,
        notes: '今天状态不错！',
        created_at: new Date().toISOString()
      })

      setTodayCheckedIn(true)
      
      // 更新统计数据
      if (stats) {
        setStats({
          ...stats,
          current_streak: stats.current_streak + 1,
          total_days: stats.total_days + 1
        })
      }
    } catch (error) {
      console.error('打卡失败:', error)
    }
  }

  const handleTaskComplete = async (taskId: string) => {
    try {
      // 模拟完成任务API调用
      await mockApiResponse({ success: true })
      
      setTodayTasks(prev => 
        prev.map(task => 
          task.id === taskId 
            ? { ...task, completed: true }
            : task
        )
      )
    } catch (error) {
      console.error('完成任务失败:', error)
    }
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 rounded-lg"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  const completedTasks = todayTasks.filter(task => task.completed).length
  const totalTasks = todayTasks.length
  const taskProgress = totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">仪表盘</h1>
        <p className="text-gray-600">欢迎回来！继续您的健康之旅</p>
      </div>

      {/* 核心指标卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Link href="/item/item-123">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">连续天数</CardTitle>
              <Flame className="h-4 w-4 text-orange-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats?.current_streak || 0}</div>
              <p className="text-xs text-muted-foreground">
                最高记录: {stats?.longest_streak || 0} 天
              </p>
            </CardContent>
          </Card>
        </Link>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总天数</CardTitle>
            <Calendar className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.total_days || 0}</div>
            <p className="text-xs text-muted-foreground">
              成功率: {stats?.success_rate || 0}%
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">正气值</CardTitle>
            <Zap className="h-4 w-4 text-yellow-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.positive_energy_score || 0}</div>
            <Progress value={stats?.positive_energy_score || 0} className="mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">今日任务</CardTitle>
            <Target className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{completedTasks}/{totalTasks}</div>
            <Progress value={taskProgress} className="mt-2" />
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 今日打卡 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <CheckCircle className="mr-2 h-5 w-5" />
              今日打卡
            </CardTitle>
            <CardDescription>
              记录您今天的状态和感受
            </CardDescription>
          </CardHeader>
          <CardContent>
            {todayCheckedIn ? (
              <div className="text-center py-8">
                <CheckCircle className="mx-auto h-16 w-16 text-green-500 mb-4" />
                <h3 className="text-lg font-semibold text-green-700 mb-2">
                  今日已打卡！
                </h3>
                <p className="text-gray-600">
                  继续保持，您做得很棒！
                </p>
              </div>
            ) : (
              <div className="text-center py-8">
                <Clock className="mx-auto h-16 w-16 text-gray-400 mb-4" />
                <h3 className="text-lg font-semibold text-gray-700 mb-4">
                  今日尚未打卡
                </h3>
                <Button 
                  onClick={handleCheckIn}
                  variant="gradient"
                  size="lg"
                  className="checkin-button"
                >
                  立即打卡
                </Button>
              </div>
            )}
          </CardContent>
        </Card>

        {/* 今日任务 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Target className="mr-2 h-5 w-5" />
              今日任务
            </CardTitle>
            <CardDescription>
              完成今天的个性化任务
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {todayTasks.map((task) => (
                <div
                  key={task.id}
                  className={`flex items-center justify-between p-3 rounded-lg border ${
                    task.completed 
                      ? 'bg-green-50 border-green-200' 
                      : 'bg-gray-50 border-gray-200'
                  }`}
                >
                  <h4 className={`font-medium flex-1 ${
                    task.completed ? 'text-green-700 line-through' : 'text-gray-900'
                  }`}>
                    {task.title}
                  </h4>
                  <p className="text-sm text-gray-600 mt-1 flex-1">
                    {task.description}
                  </p>
                  <div className="flex items-center mt-2 text-xs text-gray-500 flex-1">
                    <Clock className="mr-1 h-3 w-3" />
                    {task.estimated_minutes} 分钟
                  </div>
                  {!task.completed && (
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleTaskComplete(task.id)}
                    >
                      完成
                    </Button>
                  )}
                  {task.completed && (
                    <CheckCircle className="h-5 w-5 text-green-500" />
                  )}
                </div>
              ))}
              
              {todayTasks.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  <Award className="mx-auto h-12 w-12 mb-2" />
                  <p>今日暂无任务</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
