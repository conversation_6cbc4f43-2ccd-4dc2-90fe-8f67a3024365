'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import {
  Calendar,
  TrendingUp,
  Target,
  Zap,
  CheckCircle,
  Clock,
  Award,
  Flame,
  Sparkles,
  Shuffle,
  Heart,
  Quote,
  Users,
  Copy,
  Gift
} from 'lucide-react'
import { 
  generateMockCheckInRecords, 
  generateMockUserStats, 
  mockDailyTasks,
  mockApiResponse 
} from '@/lib/mock-data'
import { calculateStreak, formatDate, getTodayString, isToday } from '@/lib/utils'
import type { CheckInRecord, UserStats, DailyTask } from '@/types'

export default function DashboardPage() {
  const [stats, setStats] = useState<UserStats | null>(null)
  const [todayTasks, setTodayTasks] = useState<DailyTask[]>([])
  const [checkInRecords, setCheckInRecords] = useState<CheckInRecord[]>([])
  const [todayCheckedIn, setTodayCheckedIn] = useState(false)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadDashboardData()
  }, [])

  const loadDashboardData = async () => {
    try {
      // 模拟加载数据
      const [statsResponse, tasksResponse, recordsResponse] = await Promise.all([
        mockApiResponse(generateMockUserStats()),
        mockApiResponse(mockDailyTasks),
        mockApiResponse(generateMockCheckInRecords(30))
      ])

      setStats(statsResponse.data)
      setTodayTasks(tasksResponse.data)
      setCheckInRecords(recordsResponse.data)
      
      // 检查今天是否已打卡
      const todayRecord = recordsResponse.data.find(record => 
        isToday(record.check_in_date)
      )
      setTodayCheckedIn(!!todayRecord)
      
    } catch (error) {
      console.error('加载数据失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleCheckIn = async () => {
    if (todayCheckedIn) return

    try {
      // 模拟打卡API调用
      await mockApiResponse({
        id: `checkin-${Date.now()}`,
        user_id: 'mock-user-123',
        check_in_date: getTodayString(),
        status: 'success',
        mood_level: 4,
        notes: '今天状态不错！',
        created_at: new Date().toISOString()
      })

      setTodayCheckedIn(true)
      
      // 更新统计数据
      if (stats) {
        setStats({
          ...stats,
          current_streak: stats.current_streak + 1,
          total_days: stats.total_days + 1
        })
      }
    } catch (error) {
      console.error('打卡失败:', error)
    }
  }

  const handleTaskComplete = async (taskId: string) => {
    try {
      // 模拟完成任务API调用
      await mockApiResponse({ success: true })
      
      setTodayTasks(prev => 
        prev.map(task => 
          task.id === taskId 
            ? { ...task, completed: true }
            : task
        )
      )
    } catch (error) {
      console.error('完成任务失败:', error)
    }
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 rounded-lg"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  const completedTasks = todayTasks.filter(task => task.completed).length
  const totalTasks = todayTasks.length
  const taskProgress = totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">仪表盘</h1>
        <p className="text-gray-600">欢迎回来！继续您的健康之旅</p>
      </div>

      {/* 核心指标卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Link href="/item/item-123">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">连续天数</CardTitle>
              <Flame className="h-4 w-4 text-orange-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats?.current_streak || 0}</div>
              <p className="text-xs text-muted-foreground">
                最高记录: {stats?.longest_streak || 0} 天
              </p>
            </CardContent>
          </Card>
        </Link>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总天数</CardTitle>
            <Calendar className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.total_days || 0}</div>
            <p className="text-xs text-muted-foreground">
              成功率: {stats?.success_rate || 0}%
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">正气值</CardTitle>
            <Zap className="h-4 w-4 text-yellow-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.positive_energy_score || 0}</div>
            <Progress value={stats?.positive_energy_score || 0} className="mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">今日任务</CardTitle>
            <Target className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{completedTasks}/{totalTasks}</div>
            <Progress value={taskProgress} className="mt-2" />
          </CardContent>
        </Card>
      </div>

      {/* 今日Slogan */}
      <Card className="relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-600 opacity-10"></div>
        <CardContent className="relative p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center">
              <Sparkles className="h-6 w-6 text-yellow-500 mr-2" />
              <h2 className="text-lg font-bold text-gray-900">今日金句</h2>
            </div>
            <Button size="sm" variant="ghost">
              <Shuffle className="h-4 w-4" />
            </Button>
          </div>
          <blockquote className="text-xl font-medium text-gray-900 mb-3 leading-relaxed">
            "每一次选择，都是重新定义自己的机会"
          </blockquote>
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">— 戒色导师</span>
            <div className="flex items-center gap-2 text-sm text-gray-500">
              <Heart className="h-4 w-4" />
              <span>1247</span>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 今日打卡 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <CheckCircle className="mr-2 h-5 w-5" />
              今日打卡
            </CardTitle>
            <CardDescription>
              记录您今天的状态和感受
            </CardDescription>
          </CardHeader>
          <CardContent>
            {todayCheckedIn ? (
              <div className="text-center py-8">
                <CheckCircle className="mx-auto h-16 w-16 text-green-500 mb-4" />
                <h3 className="text-lg font-semibold text-green-700 mb-2">
                  今日已打卡！
                </h3>
                <p className="text-gray-600">
                  继续保持，您做得很棒！
                </p>
              </div>
            ) : (
              <div className="text-center py-8">
                <Clock className="mx-auto h-16 w-16 text-gray-400 mb-4" />
                <h3 className="text-lg font-semibold text-gray-700 mb-4">
                  今日尚未打卡
                </h3>
                <Button 
                  onClick={handleCheckIn}
                  variant="gradient"
                  size="lg"
                  className="checkin-button"
                >
                  立即打卡
                </Button>
              </div>
            )}
          </CardContent>
        </Card>

        {/* 今日任务 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Target className="mr-2 h-5 w-5" />
              今日任务
            </CardTitle>
            <CardDescription>
              完成今天的个性化任务
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {todayTasks.map((task) => (
                <div
                  key={task.id}
                  className={`flex items-center justify-between p-3 rounded-lg border ${
                    task.completed 
                      ? 'bg-green-50 border-green-200' 
                      : 'bg-gray-50 border-gray-200'
                  }`}
                >
                  <h4 className={`font-medium flex-1 ${
                    task.completed ? 'text-green-700 line-through' : 'text-gray-900'
                  }`}>
                    {task.title}
                  </h4>
                  <p className="text-sm text-gray-600 mt-1 flex-1">
                    {task.description}
                  </p>
                  <div className="flex items-center mt-2 text-xs text-gray-500 flex-1">
                    <Clock className="mr-1 h-3 w-3" />
                    {task.estimated_minutes} 分钟
                  </div>
                  {!task.completed && (
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleTaskComplete(task.id)}
                    >
                      完成
                    </Button>
                  )}
                  {task.completed && (
                    <CheckCircle className="h-5 w-5 text-green-500" />
                  )}
                </div>
              ))}
              
              {todayTasks.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  <Award className="mx-auto h-12 w-12 mb-2" />
                  <p>今日暂无任务</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* 今日正能量 */}
        <Card className="relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-r from-orange-500 to-pink-600 opacity-10"></div>
          <CardHeader className="relative">
            <CardTitle className="flex items-center">
              <Zap className="mr-2 h-5 w-5 text-orange-500" />
              今日正能量
            </CardTitle>
            <CardDescription>
              汲取智慧力量，点燃内心火焰
            </CardDescription>
          </CardHeader>
          <CardContent className="relative">
            <div className="space-y-4">
              <div className="p-4 bg-white/80 rounded-lg border">
                <div className="flex items-center gap-2 mb-2">
                  <Quote className="h-4 w-4 text-orange-500" />
                  <span className="text-sm font-medium text-gray-700">每日肯定语</span>
                </div>
                <p className="text-gray-900 font-medium">
                  "我有能力控制自己的行为和想法"
                </p>
              </div>
              <div className="grid grid-cols-3 gap-2 text-center">
                <div className="p-2 bg-white/60 rounded">
                  <div className="text-lg font-bold text-blue-600">128</div>
                  <div className="text-xs text-gray-600">精选文章</div>
                </div>
                <div className="p-2 bg-white/60 rounded">
                  <div className="text-lg font-bold text-green-600">45</div>
                  <div className="text-xs text-gray-600">挑战活动</div>
                </div>
                <div className="p-2 bg-white/60 rounded">
                  <div className="text-lg font-bold text-purple-600">67</div>
                  <div className="text-xs text-gray-600">推荐书籍</div>
                </div>
              </div>
              <div className="relative overflow-hidden rounded-lg mb-4">
                <img
                  src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=200&fit=crop"
                  alt="今日壁纸"
                  className="w-full h-24 object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent flex items-end">
                  <div className="p-2 text-white text-xs">
                    <div className="font-medium">今日壁纸：山峰日出</div>
                    <div className="opacity-80">每一次日出都是新的机会</div>
                  </div>
                </div>
              </div>
              <Button className="w-full bg-gradient-to-r from-orange-500 to-pink-600 hover:from-orange-600 hover:to-pink-700" onClick={() => window.location.href = '/inspiration'}>
                <Sparkles className="mr-2 h-4 w-4" />
                进入正能量库
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* 每日追踪快速入口 */}
        <Card className="relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-r from-green-500 to-teal-600 opacity-10"></div>
          <CardHeader className="relative">
            <CardTitle className="flex items-center">
              <Calendar className="mr-2 h-5 w-5 text-green-500" />
              每日追踪
            </CardTitle>
            <CardDescription>
              记录今日状态，追踪成长进步
            </CardDescription>
          </CardHeader>
          <CardContent className="relative">
            <div className="space-y-4">
              <div className="grid grid-cols-3 gap-3 text-center">
                <div className="p-2 bg-white/60 rounded">
                  <div className="text-lg font-bold text-green-600">4.2</div>
                  <div className="text-xs text-gray-600">今日情绪</div>
                </div>
                <div className="p-2 bg-white/60 rounded">
                  <div className="text-lg font-bold text-blue-600">7.5h</div>
                  <div className="text-xs text-gray-600">昨夜睡眠</div>
                </div>
                <div className="p-2 bg-white/60 rounded">
                  <div className="text-lg font-bold text-orange-600">23</div>
                  <div className="text-xs text-gray-600">连续天数</div>
                </div>
              </div>
              <Button className="w-full bg-gradient-to-r from-green-500 to-teal-600 hover:from-green-600 hover:to-teal-700" onClick={() => window.location.href = '/daily-tracking'}>
                <Calendar className="mr-2 h-4 w-4" />
                开始今日追踪
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* 紧急求助入口 */}
        <Card className="relative overflow-hidden border-red-200">
          <div className="absolute inset-0 bg-gradient-to-r from-red-500 to-orange-600 opacity-10"></div>
          <CardHeader className="relative">
            <CardTitle className="flex items-center">
              <AlertTriangle className="mr-2 h-5 w-5 text-red-500" />
              紧急求助
            </CardTitle>
            <CardDescription>
              遇到困难时，立即获得帮助和支持
            </CardDescription>
          </CardHeader>
          <CardContent className="relative">
            <div className="space-y-4">
              <div className="p-4 bg-white/80 rounded-lg border border-red-100">
                <div className="flex items-center gap-2 mb-2">
                  <Heart className="h-4 w-4 text-red-500" />
                  <span className="text-sm font-medium text-gray-700">24小时在线支持</span>
                </div>
                <p className="text-sm text-gray-900">
                  专业心理咨询师和戒友社区随时为你提供帮助
                </p>
              </div>
              <div className="grid grid-cols-2 gap-3">
                <Button
                  variant="outline"
                  className="border-red-200 text-red-600 hover:bg-red-50"
                  onClick={() => {
                    // 直接触发紧急求助功能
                    alert('正在为您连接紧急支持...')
                    setTimeout(() => {
                      window.location.href = '/emergency'
                    }, 1000)
                  }}
                >
                  <AlertTriangle className="mr-2 h-4 w-4" />
                  立即求助
                </Button>
                <Button
                  variant="outline"
                  onClick={() => window.location.href = '/emergency'}
                >
                  求助页面
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 邀请好友快速入口 */}
        <Card className="relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-r from-purple-500 to-pink-600 opacity-10"></div>
          <CardHeader className="relative">
            <CardTitle className="flex items-center">
              <Users className="mr-2 h-5 w-5 text-purple-500" />
              邀请好友
            </CardTitle>
            <CardDescription>
              分享邀请码，获得奖励积分
            </CardDescription>
          </CardHeader>
          <CardContent className="relative">
            <div className="space-y-4">
              <div className="p-4 bg-white/80 rounded-lg border">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-sm text-gray-600">我的邀请码</div>
                    <div className="text-lg font-bold text-purple-600 font-mono">
                      QUIT{Math.random().toString(36).substring(2, 8).toUpperCase()}
                    </div>
                  </div>
                  <Button size="sm" variant="outline">
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              <div className="grid grid-cols-3 gap-3 text-center">
                <div className="p-2 bg-white/60 rounded">
                  <div className="text-lg font-bold text-blue-600">12</div>
                  <div className="text-xs text-gray-600">总邀请</div>
                </div>
                <div className="p-2 bg-white/60 rounded">
                  <div className="text-lg font-bold text-green-600">8</div>
                  <div className="text-xs text-gray-600">成功邀请</div>
                </div>
                <div className="p-2 bg-white/60 rounded">
                  <div className="text-lg font-bold text-orange-600">240</div>
                  <div className="text-xs text-gray-600">获得积分</div>
                </div>
              </div>
              <Button className="w-full bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700" onClick={() => window.location.href = '/profile?tab=invite'}>
                <Gift className="mr-2 h-4 w-4" />
                管理邀请码
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
