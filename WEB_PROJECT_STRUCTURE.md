
# Web项目结构规划 (Supabase + Next.js)

本文档详细定义了基于 Supabase 和 Next.js (App Router) 技术栈的Web应用项目结构。该结构旨在实现关注点分离、高内聚、低耦合，并为未来的功能扩展和维护提供清晰的指导蓝图。

---

## 一、顶层目录结构

```
upbase/
├── 📁 .next/                 # Next.js 的构建输出目录
├── 📁 .vscode/                # VSCode 编辑器配置
│
├── 📁 app/                    # 核心：Next.js 13+ 的 App Router 目录
├── 📁 components/             # 全局可复用的React组件
├── 📁 lib/                    # 存放辅助函数、工具和第三方库的初始化代码
├── 📁 public/                 # 存放静态资源 (图片, favicon.ico)
│
├── middleware.ts              # 核心：Next.js中间件，用于处理路由保护
├── .gitignore                 # Git忽略文件
├── next.config.js             # Next.js 配置文件
├── package.json               # 项目依赖和脚本
├── tsconfig.json              # TypeScript 配置文件
└── README.md                  # 项目说明
```

---

## 二、核心目录详解

### 1. `app` - 路由与页面

这是项目的核心，使用 Next.js 的 App Router，目录结构直接映射到 URL 路径。

```
app/
│
├── 📁 (auth)/             # 路由组：用于认证页面，不影响URL路径
│   ├── 📁 login/           # 对应 /login 路径
│   │   └── page.tsx       # 登录页面UI
│   ├── 📁 register/        # 对应 /register 路径
│   │   └── page.tsx       # 注册页面UI
│   └── layout.tsx         # 认证页面的共享布局 (通常比较简洁)
│
├── 📁 (main)/            # 路由组：用于需要用户登录的主应用页面
│   ├── 📁 community/      # 对应 /community 路径
│   │   ├── 📁 post/[id]/  # 对应 /community/post/123 动态路径
│   │   │   └── page.tsx   # 帖子详情页
│   │   └── page.tsx       # 社区信息流页面
│   │
│   ├── 📁 journal/       # 对应 /journal 路径
│   │   └── page.tsx       # 日记/打卡主页
│   │
│   ├── 📁 profile/       # 对应 /profile 路径
│   │   └── page.tsx       # 个人中心页面
│   │
│   ├── 📁 settings/      # 对应 /settings 路径
│   │   └── page.tsx       # 设置页面
│   │
│   ├── page.tsx           # 主应用的首页/仪表盘 (对应 / 路径)
│   └── layout.tsx         # 主应用页面的共享布局 (包含导航栏、侧边栏等)
│
├── 📁 api/                 # 后端API路由 (例如: /api/posts)
│   └── ...
│
├── layout.tsx             # 全局根布局 (包含<html>和<body>)
└── globals.css            # 全局CSS样式
```

*   **路由组 `(auth)` 和 `(main)`**: 这是实现不同页面使用不同布局的关键。括号包裹的目录不会成为URL的一部分。例如 `(auth)/login` 的路径就是 `/login`。
*   **动态路由 `[id]`**: 用于展示具体条目，如帖子详情。

### 2. `components` - 可复用组件

所有非页面的 React 组件都存放在这里，按功能和复用范围组织。

```
components/
├── 📁 ui/                  # 基础UI组件 (类似shadcn/ui, 如 Button, Card, Input)
├── 📁 auth/                # 认证相关的复合组件 (LoginForm, RegisterForm)
├── 📁 layout/              # 布局组件 (Navbar, Sidebar, Footer)
└── 📁 features/            # 特定功能的组件 (JournalCalendar, PostCard)
```

*   **`ui/`**: 存放原子级的、无业务逻辑的UI组件，保证视觉风格统一。
*   **`features/`**: 存放与特定业务功能（如日记、社区）紧密相关的组件，提高内聚性。

### 3. `lib` - 辅助与工具

存放项目的主要逻辑、配置和工具函数。

```
lib/
├── supabase.ts            # Supabase客户端的初始化和辅助函数
├── utils.ts               # 通用工具函数 (日期格式化、字符串处理等)
└── types.ts               # 全局 TypeScript 类型定义
```

*   **`supabase.ts`**: 项目与 Supabase 交互的入口，负责初始化客户端并封装常用的数据库和认证操作。

### 4. `middleware.ts` - 路由保护

这是一个在服务器边缘运行的函数，用于在用户访问受保护页面前进行身份验证。

*   **核心逻辑**: 检查请求的路径是否属于 `(main)` 路由组。如果是，则通过 Supabase 验证用户会话（Session）。如果会话无效，则将用户重定向到 `/login` 页面。

---

## 三、工作流程说明

1.  **页面开发**: 在 `app/` 目录下创建新的文件夹和 `page.tsx` 文件来定义新的页面路由。
2.  **组件开发**: 在 `components/` 目录下创建可复用的组件，然后在页面中导入使用。
3.  **数据交互**: 在服务端组件或客户端组件中，从 `lib/supabase.ts` 导入 Supabase 客户端实例，进行数据查询、用户认证等操作。
4.  **状态管理**: 对于简单场景，优先使用 React Server Components 和 URL state。对于复杂的客户端交互，可引入轻量级状态管理库（如 Zustand），并在 `lib/` 下创建 `store.ts`。

