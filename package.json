{"name": "upbase", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-slot": "^1.2.3", "@supabase/ssr": "^0.4.0", "@supabase/supabase-js": "^2.45.0", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^3.6.0", "echarts": "^5.6.0", "echarts-for-react": "^3.0.2", "lucide-react": "^0.525.0", "next": "14.2.5", "react": "^18", "react-day-picker": "^8.10.1", "react-dom": "^18", "recharts": "^2.12.7", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "zustand": "^4.5.4"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.2.5", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}