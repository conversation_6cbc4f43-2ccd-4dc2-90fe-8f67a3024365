# UI/UX 视觉与交互指南

本文档定义了应用的官方视觉识别系统（VI）和用户体验（UX）交互原则，旨在确保品牌一致性、提升可用性，并为用户创造一个积极、支持性的数字环境。所有设计师和开发者都应遵循此指南。

---

## 1. 品牌标识 (Branding)

-   **应用名称**: Upbase
-   **Logo**: (当前为占位符) 一个由两个部分组成的抽象图形。左侧是一个向上的箭头，象征着进步和提升；右侧是一个坚实的盾牌，代表着保护和支持。整体形成一个稳定而积极的形象。
-   **标语 (Slogan)**: “重塑习惯，掌控生活。” (Reshape Habits, Reclaim Life.)

---

## 2. 色彩规范 (Color Palette)

色彩系统旨在营造一种平静、积极和值得信赖的感觉。

```mermaid
graph TD
    subgraph 主要色系
        A["**主色 (Primary)**<br>#3B82F6 (Blue-500)<br>用于主要按钮、链接、焦点状态"] --> B["**辅色 (Secondary)**<br>#F9FAFB (Gray-50)<br>用于次要按钮、背景"] 
    end
    subgraph 中性色
        C["**基础背景 (Background)**<br>#FFFFFF"] --> D["**卡片/纸张背景 (Surface)**<br>#F9FAFB (Gray-50)"]
        D --> E["**边框/分割线 (Border)**<br>#E5E7EB (Gray-200)"]
        E --> F["**主要文本 (Text-Primary)**<br>#1F2937 (Gray-800)"]
        F --> G["**次要文本 (Text-Secondary)**<br>#6B7280 (Gray-500)"]
    end
    subgraph 功能色
        H["**成功 (Success)**<br>#10B981 (Green-500)"]
        I["**警告 (Warning)**<br>#F59E0B (Amber-500)"]
        J["**危险/错误 (Danger)**<br>#EF4444 (Red-500)"]
    end
```

---

## 3. 字体排印 (Typography)

我们选择清晰、易读的无衬线字体 `Inter` 作为应用的唯一字体，以确保在所有设备和尺寸上的可读性。

-   **字体家族**: `Inter`, sans-serif

| 元素             | 字重 (Weight) | 大小 (Size) | 行高 (Line Height) | 用途                               |
| ---------------- | ------------- | ----------- | ------------------ | ---------------------------------- |
| **Display (h1)** | Bold (700)    | 48px        | 60px               | 英雄区域的大标题                   |
| **Heading (h2)** | Bold (700)    | 36px        | 44px               | 页面主标题                         |
| **Heading (h3)** | SemiBold (600)| 24px        | 32px               | 卡片或区域标题                     |
| **Heading (h4)** | SemiBold (600)| 20px        | 28px               | 次级标题                           |
| **Body (p)**     | Regular (400) | 16px        | 24px               | 正文、段落、长文本                 |
| **Label**        | Medium (500)  | 14px        | 20px               | 表单标签、信息性文本               |
| **Caption**      | Regular (400) | 12px        | 16px               | 辅助性文本、图表说明               |

---

## 4. 图标库 (Iconography)

-   **图标库**: **Lucide Icons** (`lucide-react`)
-   **使用原则**: 
    -   保持线性、简洁的风格。
    -   默认尺寸为 `20px` x `20px`。
    -   颜色应使用中性色（如 `Gray-500`），除非用于表示特定状态（如成功、危险）。

---

## 5. 布局与间距 (Layout & Spacing)

-   **基础单位**: `4px`。所有间距、边距、填充都应是此单位的倍数。
-   **常用间距**:
    -   `xs`: 4px (1x)
    -   `sm`: 8px (2x)
    -   `md`: 16px (4x)
    -   `lg`: 24px (6x)
    -   `xl`: 32px (8x)
    -   `2xl`: 48px (12x)
-   **栅格系统**: 采用12列栅格系统进行页面布局。
-   **最大内容宽度**: 页面主要内容区域的最大宽度为 `1280px`，并在屏幕中央对齐。

---

## 6. 组件视觉规范 (Core Components)

### **按钮 (Button)**
-   **圆角**: `8px` (Medium)
-   **主要按钮**: 蓝色背景 (`Blue-500`)，白色文字。
-   **次要按钮**: 灰色背景 (`Gray-50`)，深灰色文字 (`Gray-800`)，带边框 (`Gray-200`)。
-   **危险按钮**: 红色背景 (`Red-500`)，白色文字。
-   **交互**: `hover` 状态下，背景色变亮10%；`active` 状态下，背景色变暗10%。

### **输入框 (Input)**
-   **圆角**: `8px` (Medium)
-   **背景**: 白色 (`#FFFFFF`)
-   **边框**: 默认状态为 `Gray-300`，`focus` 状态为 `Blue-500`，`error` 状态为 `Red-500`。
-   **内边距**: `12px` (水平), `10px` (垂直)。

### **卡片 (Card)**
-   **背景**: `Gray-50`
-   **圆角**: `16px` (Large)
-   **边框**: `1px solid #E5E7EB` (Gray-200)
-   **阴影**: `0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)` (微弱阴影)
-   **内边距**: `24px`

---

## 7. 动效设计 (Motion Design)

-   **原则**: 动效应 **有意义、快速且不打扰**。其主要目的是引导用户注意力、提供反馈和增强空间感。
-   **持续时间**: 大多数UI交互动效应在 `150ms` 到 `300ms` 之间完成。
-   **缓动函数**: 默认使用 `ease-in-out`，创造自然、平滑的过渡效果。
-   **应用场景**:
    -   **状态变化**: 组件状态（如按钮hover、输入框focus）的颜色和阴影变化应有平滑过渡。
    -   **元素进入/退出**: 列表项、弹窗等元素的出现和消失应伴随轻微的淡入淡出（`fade`）和位移效果。
    -   **加载状态**: 使用骨架屏（Skeleton Screens）或平滑的加载指示器，而不是突兀的全局加载动画。
