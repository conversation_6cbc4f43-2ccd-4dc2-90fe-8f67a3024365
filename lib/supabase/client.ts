import { createClient, SupabaseClient } from '@supabase/supabase-js'

// 1. 定义模拟客户端
// 我们为 mockSupabase 提供一个明确的类型，使其与 SupabaseClient 的结构部分兼容
export const mockSupabase: any = {
  auth: {
    signUp: async (credentials: { email: string; password: string }) => {
      await new Promise(resolve => setTimeout(resolve, 1000))
      return {
        data: {
          user: {
            id: 'mock-user-id-' + Date.now(),
            email: credentials.email,
            created_at: new Date().toISOString(),
          },
          session: {
            access_token: 'mock-access-token',
            refresh_token: 'mock-refresh-token',
          }
        },
        error: null
      }
    },
    
    signInWithPassword: async (credentials: { email: string; password: string }) => {
      await new Promise(resolve => setTimeout(resolve, 1000))
      if (credentials.email === '<EMAIL>') {
        return {
          data: { user: null, session: null },
          error: { message: '邮箱或密码错误' }
        }
      }
      return {
        data: {
          user: {
            id: 'mock-user-id-123',
            email: credentials.email,
            created_at: new Date().toISOString(),
          },
          session: {
            access_token: 'mock-access-token',
            refresh_token: 'mock-refresh-token',
          }
        },
        error: null
      }
    },
    
    signOut: async () => {
      await new Promise(resolve => setTimeout(resolve, 500))
      return { error: null }
    },
    
    getSession: async () => {
      const mockSession = localStorage.getItem('mock-session')
      if (mockSession) {
        return {
          data: { session: JSON.parse(mockSession) },
          error: null
        }
      }
      return { data: { session: null }, error: null }
    },
    
    onAuthStateChange: (callback: (event: string, session: any) => void) => {
      return {
        data: {
          subscription: {
            unsubscribe: () => {}
          }
        }
      }
    }
  },
  
  from: (table: string) => ({
    select: (columns?: string) => ({
      eq: (column: string, value: any) => ({
        single: async () => {
          await new Promise(resolve => setTimeout(resolve, 300))
          return { data: null, error: null }
        }
      }),
      order: (column: string, options?: any) => ({
        limit: (count: number) => ({
          async: async () => {
            await new Promise(resolve => setTimeout(resolve, 300))
            return { data: [], error: null }
          }
        })
      })
    }),
    
    insert: (data: any) => ({
      select: () => ({
        single: async () => {
          await new Promise(resolve => setTimeout(resolve, 500))
          return {
            data: { ...data, id: 'mock-id-' + Date.now() },
            error: null
          }
        }
      })
    }),
    
    update: (data: any) => ({
      eq: (column: string, value: any) => ({
        select: () => ({
          single: async () => {
            await new Promise(resolve => setTimeout(resolve, 500))
            return { data: { ...data }, error: null }
          }
        })
      })
    }),
    
    delete: () => ({
      eq: (column: string, value: any) => ({
        async: async () => {
          await new Promise(resolve => setTimeout(resolve, 500))
          return { data: null, error: null }
        }
      })
    })
  })
}

// 2. 获取环境变量
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

// 3. 声明一个变量来持有 Supabase 客户端实例
// 它的类型可以是真实的 SupabaseClient，也可以是我们的模拟对象
let supabase: SupabaseClient | typeof mockSupabase;

// 4. 使用清晰的 if/else 结构来赋值
// 这种方式能让 TypeScript 准确地进行类型推断
if (supabaseUrl && supabaseAnonKey) {
  // 在这个块中，TypeScript 知道 supabaseUrl 和 supabaseAnonKey 都是字符串
  supabase = createClient(supabaseUrl, supabaseAnonKey)
} else {
  // 否则，使用模拟客户端
  supabase = mockSupabase
}

// 5. 导出最终的客户端实例和模拟客户端
export { supabase }
export default mockSupabase