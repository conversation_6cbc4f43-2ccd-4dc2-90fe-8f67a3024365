import { createClient } from '@supabase/supabase-js'

// 模拟的 Supabase 配置
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://mock-supabase-url.supabase.co'
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'mock-anon-key'

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// 由于我们使用模拟数据，这里创建一个模拟的客户端
export const mockSupabase = {
  auth: {
    signUp: async (credentials: { email: string; password: string }) => {
      // 模拟注册
      await new Promise(resolve => setTimeout(resolve, 1000))
      return {
        data: {
          user: {
            id: 'mock-user-id-' + Date.now(),
            email: credentials.email,
            created_at: new Date().toISOString(),
          },
          session: {
            access_token: 'mock-access-token',
            refresh_token: 'mock-refresh-token',
          }
        },
        error: null
      }
    },
    
    signInWithPassword: async (credentials: { email: string; password: string }) => {
      // 模拟登录
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // 模拟登录失败的情况
      if (credentials.email === '<EMAIL>') {
        return {
          data: { user: null, session: null },
          error: { message: '邮箱或密码错误' }
        }
      }
      
      return {
        data: {
          user: {
            id: 'mock-user-id-123',
            email: credentials.email,
            created_at: new Date().toISOString(),
          },
          session: {
            access_token: 'mock-access-token',
            refresh_token: 'mock-refresh-token',
          }
        },
        error: null
      }
    },
    
    signOut: async () => {
      await new Promise(resolve => setTimeout(resolve, 500))
      return { error: null }
    },
    
    getSession: async () => {
      // 模拟获取当前会话
      const mockSession = localStorage.getItem('mock-session')
      if (mockSession) {
        return {
          data: {
            session: JSON.parse(mockSession)
          },
          error: null
        }
      }
      return {
        data: { session: null },
        error: null
      }
    },
    
    onAuthStateChange: (callback: (event: string, session: any) => void) => {
      // 模拟认证状态变化监听
      return {
        data: {
          subscription: {
            unsubscribe: () => {}
          }
        }
      }
    }
  },
  
  from: (table: string) => ({
    select: (columns?: string) => ({
      eq: (column: string, value: any) => ({
        single: async () => {
          // 模拟数据查询
          await new Promise(resolve => setTimeout(resolve, 300))
          return { data: null, error: null }
        }
      }),
      order: (column: string, options?: any) => ({
        limit: (count: number) => ({
          async: async () => {
            await new Promise(resolve => setTimeout(resolve, 300))
            return { data: [], error: null }
          }
        })
      })
    }),
    
    insert: (data: any) => ({
      select: () => ({
        single: async () => {
          await new Promise(resolve => setTimeout(resolve, 500))
          return {
            data: { ...data, id: 'mock-id-' + Date.now() },
            error: null
          }
        }
      })
    }),
    
    update: (data: any) => ({
      eq: (column: string, value: any) => ({
        select: () => ({
          single: async () => {
            await new Promise(resolve => setTimeout(resolve, 500))
            return { data: { ...data }, error: null }
          }
        })
      })
    }),
    
    delete: () => ({
      eq: (column: string, value: any) => ({
        async: async () => {
          await new Promise(resolve => setTimeout(resolve, 500))
          return { data: null, error: null }
        }
      })
    })
  })
}

// 导出模拟客户端作为默认客户端
export default mockSupabase
