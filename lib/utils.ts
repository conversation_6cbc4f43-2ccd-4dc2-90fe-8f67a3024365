import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"
import type { CheckInRecord } from '@/types';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function isValidEmail(email: string): boolean {
  const emailRegex = /^[\w-\.@]+@([\w-]+\.)+[\w-]{2,4}$/;
  return emailRegex.test(email);
}

export function getTodayString(): string {
  return new Date().toISOString().split('T')[0];
}

export function isToday(dateString: string): boolean {
  return dateString === getTodayString();
}

export function formatDate(dateString: string): string {
  const date = new Date(dateString);
  return date.toLocaleDateString('zh-CN', { month: 'long', day: 'numeric' });
}

export function calculateStreak(records: CheckInRecord[]): number {
  if (records.length === 0) return 0;

  let streak = 0;
  const today = new Date();
  const todayString = getTodayString();

  // 对记录按日期降序排序
  const sortedRecords = [...records].sort((a, b) => 
    new Date(b.check_in_date).getTime() - new Date(a.check_in_date).getTime()
  );

  // 检查今天是否已打卡
  const todayRecord = sortedRecords.find(r => r.check_in_date === todayString);
  if (todayRecord) {
    streak = 1;
    let lastDate = new Date(todayString);

    // 从昨天开始计算连续天数
    for (let i = 1; i < sortedRecords.length; i++) {
      const recordDate = new Date(sortedRecords[i].check_in_date);
      const expectedDate = new Date(lastDate);
      expectedDate.setDate(lastDate.getDate() - 1);

      if (recordDate.toISOString().split('T')[0] === expectedDate.toISOString().split('T')[0]) {
        streak++;
        lastDate = recordDate;
      } else {
        break; // 连续中断
      }
    }
  } else {
    // 如果今天没打卡，检查昨天是否打卡
    const yesterday = new Date(today);
    yesterday.setDate(today.getDate() - 1);
    const yesterdayString = yesterday.toISOString().split('T')[0];
    const yesterdayRecord = sortedRecords.find(r => r.check_in_date === yesterdayString);

    if (yesterdayRecord) {
        streak = 1;
        let lastDate = new Date(yesterdayString);

        // 从前天开始计算
        for (let i = 1; i < sortedRecords.length; i++) {
            const recordDate = new Date(sortedRecords[i].check_in_date);
            const expectedDate = new Date(lastDate);
            expectedDate.setDate(lastDate.getDate() - 1);

            if (recordDate.toISOString().split('T')[0] === expectedDate.toISOString().split('T')[0]) {
                streak++;
                lastDate = recordDate;
            } else {
                break;
            }
        }
    }
  }

  return streak;
}

export function formatRelativeDate(dateString: string): string {
  const date = new Date(dateString);
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  const rtf = new Intl.RelativeTimeFormat('zh-CN', { numeric: 'auto' });

  if (diffInSeconds < 60) {
    return rtf.format(-diffInSeconds, 'second');
  }

  const diffInMinutes = Math.floor(diffInSeconds / 60);
  if (diffInMinutes < 60) {
    return rtf.format(-diffInMinutes, 'minute');
  }

  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) {
    return rtf.format(-diffInHours, 'hour');
  }

  const diffInDays = Math.floor(diffInHours / 24);
  return rtf.format(-diffInDays, 'day');
}