import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"
import { format, differenceInDays, startOfDay, endOfDay } from "date-fns"
import { zhCN } from "date-fns/locale"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// 日期格式化工具
export function formatDate(date: Date | string, formatStr: string = 'yyyy-MM-dd'): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date
  return format(dateObj, formatStr, { locale: zhCN })
}

export function formatRelativeDate(date: Date | string): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date
  const now = new Date()
  const diffDays = differenceInDays(now, dateObj)
  
  if (diffDays === 0) return '今天'
  if (diffDays === 1) return '昨天'
  if (diffDays === 2) return '前天'
  if (diffDays < 7) return `${diffDays}天前`
  if (diffDays < 30) return `${Math.floor(diffDays / 7)}周前`
  if (diffDays < 365) return `${Math.floor(diffDays / 30)}个月前`
  return `${Math.floor(diffDays / 365)}年前`
}

// 计算连续天数
export function calculateStreak(checkInRecords: Array<{ check_in_date: string; status: string }>): number {
  if (!checkInRecords.length) return 0
  
  // 按日期降序排序
  const sortedRecords = checkInRecords
    .sort((a, b) => new Date(b.check_in_date).getTime() - new Date(a.check_in_date).getTime())
  
  let streak = 0
  const today = startOfDay(new Date())
  
  for (const record of sortedRecords) {
    const recordDate = startOfDay(new Date(record.check_in_date))
    const expectedDate = startOfDay(new Date(today.getTime() - streak * 24 * 60 * 60 * 1000))
    
    if (recordDate.getTime() === expectedDate.getTime() && record.status === 'success') {
      streak++
    } else {
      break
    }
  }
  
  return streak
}

// 计算成功率
export function calculateSuccessRate(checkInRecords: Array<{ status: string }>): number {
  if (!checkInRecords.length) return 0
  
  const successCount = checkInRecords.filter(record => record.status === 'success').length
  return Math.round((successCount / checkInRecords.length) * 100)
}

// 生成匿名ID
export function generateAnonymousId(): string {
  const adjectives = ['勇敢的', '坚强的', '智慧的', '温和的', '积极的', '乐观的', '努力的', '专注的']
  const nouns = ['战士', '学者', '探索者', '守护者', '修行者', '追梦人', '行者', '智者']
  
  const adjective = adjectives[Math.floor(Math.random() * adjectives.length)]
  const noun = nouns[Math.floor(Math.random() * nouns.length)]
  const number = Math.floor(Math.random() * 1000)
  
  return `${adjective}${noun}${number}`
}

// 正气值计算
export function calculatePositiveEnergyScore(
  checkInRecords: Array<{ status: string; check_in_date: string }>,
  baseScore: number = 80
): number {
  let score = baseScore
  
  checkInRecords.forEach(record => {
    if (record.status === 'success') {
      score += 0.2
    } else if (record.status === 'relapse') {
      score -= 3
    }
  })
  
  // 确保分数在 0-100 范围内
  return Math.max(0, Math.min(100, Math.round(score * 10) / 10))
}

// 获取今日日期字符串
export function getTodayString(): string {
  return format(new Date(), 'yyyy-MM-dd')
}

// 检查是否为今天
export function isToday(date: Date | string): boolean {
  const dateObj = typeof date === 'string' ? new Date(date) : date
  const today = new Date()
  return format(dateObj, 'yyyy-MM-dd') === format(today, 'yyyy-MM-dd')
}

// 生成日期范围
export function generateDateRange(startDate: Date, endDate: Date): string[] {
  const dates: string[] = []
  const current = new Date(startDate)
  
  while (current <= endDate) {
    dates.push(format(current, 'yyyy-MM-dd'))
    current.setDate(current.getDate() + 1)
  }
  
  return dates
}

// 验证邮箱格式
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

// 验证密码强度
export function validatePassword(password: string): { isValid: boolean; message: string } {
  if (password.length < 8) {
    return { isValid: false, message: '密码长度至少8位' }
  }
  
  if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(password)) {
    return { isValid: false, message: '密码必须包含大小写字母和数字' }
  }
  
  return { isValid: true, message: '密码强度良好' }
}

// 防抖函数
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}
