import { 
  User, 
  CheckInRecord, 
  RecoveryPlan, 
  Post, 
  Comment, 
  UserStats,
  DailyTask,
  Milestone 
} from '@/types'
import { format, subDays, addDays } from 'date-fns'

// 模拟用户数据
export const mockUser: User = {
  id: 'mock-user-123',
  email: '<EMAIL>',
  username: '坚持的小明',
  avatar_url: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
  created_at: '2024-01-01T00:00:00Z',
  profile: {
    age: 25,
    goal: 'quit_porn',
    history: 'tried_failed',
    triggers: ['深夜独处', '压力大', '无聊'],
    positive_energy_score: 85.6
  }
}

// 生成模拟打卡记录
export function generateMockCheckInRecords(days: number = 30): CheckInRecord[] {
  const records: CheckInRecord[] = []
  const today = new Date()
  
  for (let i = 0; i < days; i++) {
    const date = subDays(today, i)
    const isSuccess = Math.random() > 0.2 // 80% 成功率
    
    records.push({
      id: `checkin-${i}`,
      user_id: mockUser.id,
      check_in_date: format(date, 'yyyy-MM-dd'),
      status: isSuccess ? 'success' : 'relapse',
      mood_level: Math.floor(Math.random() * 5) + 1 as 1 | 2 | 3 | 4 | 5,
      notes: isSuccess ? '今天状态不错，继续加油！' : '遇到了一些挑战，明天会更好',
      challenges: isSuccess ? [] : ['压力大', '情绪低落'],
      created_at: date.toISOString()
    })
  }
  
  return records.reverse() // 按时间正序排列
}

// 模拟每日任务
export const mockDailyTasks: DailyTask[] = [
  {
    id: 'task-1',
    title: '晨间冥想',
    description: '进行10分钟的正念冥想，专注呼吸，平静内心',
    category: 'mindfulness',
    difficulty: 2,
    estimated_minutes: 10,
    completed: false,
    task_date: format(new Date(), 'yyyy-MM-dd')
  },
  {
    id: 'task-2',
    title: '户外运动',
    description: '进行30分钟的户外运动，如跑步、散步或骑行',
    category: 'physical',
    difficulty: 3,
    estimated_minutes: 30,
    completed: true,
    task_date: format(new Date(), 'yyyy-MM-dd')
  },
  {
    id: 'task-3',
    title: '阅读学习',
    description: '阅读一篇关于自我提升的文章或书籍章节',
    category: 'learning',
    difficulty: 2,
    estimated_minutes: 20,
    completed: false,
    task_date: format(new Date(), 'yyyy-MM-dd')
  }
]

// 模拟里程碑
export const mockMilestones: Milestone[] = [
  {
    id: 'milestone-1',
    title: '第一周',
    description: '成功坚持一周，建立初步习惯',
    target_days: 7,
    achieved: true,
    achieved_at: '2024-01-07T00:00:00Z'
  },
  {
    id: 'milestone-2',
    title: '第一个月',
    description: '坚持一个月，证明你的决心',
    target_days: 30,
    achieved: true,
    achieved_at: '2024-01-30T00:00:00Z'
  },
  {
    id: 'milestone-3',
    title: '三个月',
    description: '坚持三个月，习惯已经养成',
    target_days: 90,
    achieved: false
  }
]

// 模拟戒色计划
export const mockRecoveryPlan: RecoveryPlan = {
  id: 'plan-123',
  user_id: mockUser.id,
  status: 'active',
  plan_data: {
    duration_days: 90,
    daily_tasks: mockDailyTasks,
    milestones: mockMilestones,
    strategies: [
      {
        id: 'strategy-1',
        title: '深呼吸技巧',
        description: '当感到冲动时，进行4-7-8呼吸法',
        category: 'intervention',
        triggers: ['压力大', '情绪波动']
      },
      {
        id: 'strategy-2',
        title: '转移注意力',
        description: '立即离开当前环境，进行其他活动',
        category: 'prevention',
        triggers: ['深夜独处', '无聊']
      }
    ]
  },
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z'
}

// 模拟社区帖子
export const mockPosts: Post[] = [
  {
    id: 'post-1',
    user_id: 'user-1',
    anonymous_id: '坚强的战士001',
    title: '坚持30天的心得分享',
    content: '大家好，我已经坚持30天了！想分享一些心得...',
    type: 'share',
    tags: ['经验分享', '30天'],
    likes_count: 15,
    comments_count: 8,
    is_moderated: false,
    created_at: '2024-01-15T10:00:00Z',
    updated_at: '2024-01-15T10:00:00Z'
  },
  {
    id: 'post-2',
    user_id: 'user-2',
    anonymous_id: '努力的学者002',
    title: '遇到挫折怎么办？',
    content: '最近遇到了一些挑战，想请教大家的经验...',
    type: 'question',
    tags: ['求助', '挫折'],
    likes_count: 8,
    comments_count: 12,
    is_moderated: false,
    created_at: '2024-01-14T15:30:00Z',
    updated_at: '2024-01-14T15:30:00Z'
  }
]

// 模拟用户统计数据
export function generateMockUserStats(): UserStats {
  const checkInRecords = generateMockCheckInRecords(30)
  
  return {
    current_streak: 12,
    longest_streak: 25,
    total_days: 85,
    success_rate: 82,
    positive_energy_score: 85.6,
    mood_trend: checkInRecords.slice(-7).map(record => ({
      date: record.check_in_date,
      mood: record.mood_level
    })),
    weekly_progress: [
      { week: '第1周', success_days: 6, total_days: 7, success_rate: 86 },
      { week: '第2周', success_days: 7, total_days: 7, success_rate: 100 },
      { week: '第3周', success_days: 5, total_days: 7, success_rate: 71 },
      { week: '第4周', success_days: 6, total_days: 7, success_rate: 86 }
    ]
  }
}

// 模拟API延迟
export const mockApiDelay = (ms: number = 500) => 
  new Promise(resolve => setTimeout(resolve, ms))

// 模拟API响应
export async function mockApiResponse<T>(data: T, delay: number = 500): Promise<{ data: T; error: null }> {
  await mockApiDelay(delay)
  return { data, error: null }
}
