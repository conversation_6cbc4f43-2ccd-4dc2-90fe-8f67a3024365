# 部署与运维手册

本文档为应用的部署、监控、扩展和灾备提供了详细的技术方案和操作指南。

---

## 1. 部署架构与环境

-   **前端 (Next.js)**: 部署于 **Vercel**。
    -   **生产环境**: `main` 分支自动部署到生产域名。
    -   **预览环境**: 每个 Pull Request 会自动生成一个独立的预览URL，用于测试和审查。
    -   **环境变量**: 通过 Vercel UI 管理生产、预览和开发环境的环境变量（如 `NEXT_PUBLIC_SUPABASE_URL`, `NEXT_PUBLIC_SUPABASE_ANON_KEY`）。
-   **后端 (BaaS)**: 使用 **Supabase** Cloud。
    -   **项目设置**: 在 Supabase Studio 中配置数据库、认证、存储和API访问规则。
    -   **网络策略**: 配置严格的网络访问策略，仅允许来自Vercel部署IP范围的请求访问数据库（如果需要直接访问）。
    -   **密钥管理**: Supabase 的 `anon` 和 `service_role` 密钥通过安全渠道注入到Vercel的环境变量中。

---

## 2. CI/CD 流程详解

```mermaid
graph TD
    A[开发者] -- git push --> B(GitHub Repo)
    B -- PR --> C{触发 GitHub Action}
    C -- 运行 --> D[Lint & Type Check]
    C -- 运行 --> E[Unit & Integration Tests]
    D & E --> F{Vercel Build}
    F -- 成功 --> G[生成预览部署]
    A -- Merge PR --> H(main 分支)
    H -- Webhook --> I{触发 GitHub Action}
    I -- 运行 --> J[Lint, Test, Build]
    J -- 成功 --> K[部署到生产环境]
    K -- 通知 --> L(Slack/Email)
```

-   **触发器**: 对 `main` 分支的推送和对其他分支的 Pull Request 会触发 GitHub Actions 工作流。
-   **作业 (Jobs)**:
    1.  **Lint & Type Check**: 运行 `eslint .` 和 `tsc --noEmit` 确保代码质量和类型安全。
    2.  **Testing**: 运行 `jest` 或 `vitest` 执行所有单元测试和集成测试。测试将连接到一个独立的Supabase测试项目或使用mock数据。
    3.  **Build**: 如果测试通过，Vercel CLI (或Vercel for GitHub集成) 将执行 `vercel build`。
    4.  **Deploy**: Vercel 将构建产物部署到相应的环境。
-   **通知**: 生产部署成功或失败后，通过Slack或邮件通知开发团队。

---

## 3. 监控、日志与告警

-   **前端监控 (Sentry)**:
    -   **配置**: 在 `next.config.js` 和应用入口处集成 Sentry SDK。
    -   **Source Maps**: 在CI/CD流程中自动上传Source Maps到Sentry，以便于调试压缩后的代码。
    -   **性能监控**: 启用 Tracing 来监控页面加载、API请求等性能指标，采样率根据环境调整（生产环境较低，开发环境较高）。
    -   **告警规则**: 配置告警规则，当错误率超过阈值、或出现新的严重错误时，立即通知到 Slack 的 `#alerts` 频道。
-   **后端监控 (Supabase)**:
    -   **日志**: 使用 Supabase Studio 的 Log Explorer 查看 API 请求日志、数据库查询日志和认证事件。
    -   **报告**: 定期审查 Supabase 生成的内置报告，分析API流量、慢查询和资源使用情况。
    -   **数据库健康**: 监控数据库的CPU使用率、连接数和磁盘I/O，配置告警（通过第三方工具或自定义脚本）以防止性能瓶颈。
-   **站点可用性**: 使用 **UptimeRobot** 或类似服务，从外部网络每5分钟检查一次生产环境主页和核心API端点的可用性。

---

## 4. 扩展性与灾难恢复

-   **扩展性**:
    -   **前端**: Vercel 的 Serverless 架构可根据流量自动弹性伸缩，无需手动干预。
    -   **后端**: Supabase 默认配置足以应对初期流量。随着用户增长，可以平滑升级到更高的计算资源套餐。对于查询密集型应用，将通过优化查询、增加索引和使用物化视图来提升性能。
-   **灾难恢复**:
    -   **数据库备份**: Supabase 提供每日自动备份和按时间点恢复 (PITR) 功能。我们将定期（至少每季度一次）进行一次恢复演练，将备份恢复到一个临时项目中以验证其完整性。
    -   **服务中断**: 如果 Vercel 或 Supabase 发生区域性服务中断，我们将通过官方状态页面（status.vercel.com, status.supabase.com）获取信息，并通过社交媒体向用户通报情况。由于是全球分布式服务，完全中断的可能性较低。
    -   **数据导出**: 除了Supabase的备份，我们还将设置一个每周运行的GitHub Action，使用 `pg_dump` 将数据库结构和数据导出并安全地存储在私有存储桶中，作为额外的灾备措施。
